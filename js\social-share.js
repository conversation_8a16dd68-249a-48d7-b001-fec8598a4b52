/**
 * Bawana News - Social Share Functionality
 * Menangani fungsi copy link dan interaksi tombol share
 */

jQuery(document).ready(function($) {
    
    // Fungsi copy link ke clipboard
    $('.share-btn.copy-link').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var url = $button.data('url');
        
        // Coba copy menggunakan Clipboard API (modern browsers)
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(url).then(function() {
                showCopySuccess($button);
            }).catch(function() {
                fallbackCopyText(url, $button);
            });
        } else {
            // Fallback untuk browser lama
            fallbackCopyText(url, $button);
        }
    });
    
    // Fungsi fallback untuk copy text
    function fallbackCopyText(text, $button) {
        // Buat textarea sementara
        var $temp = $('<textarea>');
        $('body').append($temp);
        $temp.val(text).select();
        
        try {
            var successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess($button);
            } else {
                showCopyError($button);
            }
        } catch (err) {
            showCopyError($button);
        }
        
        $temp.remove();
    }
    
    // Tampilkan feedback sukses
    function showCopySuccess($button) {
        var originalIcon = $button.find('i').attr('class');
        var originalTitle = $button.attr('title');
        
        // Ubah ikon dan warna
        $button.find('i').attr('class', 'fas fa-check');
        $button.addClass('copied');
        $button.attr('title', bawanaShare.copied_text);
        
        // Kembalikan ke normal setelah 2 detik
        setTimeout(function() {
            $button.find('i').attr('class', originalIcon);
            $button.removeClass('copied');
            $button.attr('title', originalTitle);
        }, 2000);
    }
    
    // Tampilkan feedback error
    function showCopyError($button) {
        var originalIcon = $button.find('i').attr('class');
        var originalTitle = $button.attr('title');
        
        // Ubah ikon ke error
        $button.find('i').attr('class', 'fas fa-exclamation-triangle');
        $button.attr('title', bawanaShare.copy_error);
        
        // Kembalikan ke normal setelah 2 detik
        setTimeout(function() {
            $button.find('i').attr('class', originalIcon);
            $button.attr('title', originalTitle);
        }, 2000);
    }
    
    // Tracking untuk analytics (opsional)
    $('.share-btn').on('click', function() {
        var platform = '';
        if ($(this).hasClass('whatsapp')) platform = 'WhatsApp';
        else if ($(this).hasClass('facebook')) platform = 'Facebook';
        else if ($(this).hasClass('twitter')) platform = 'Twitter';
        else if ($(this).hasClass('copy-link')) platform = 'Copy Link';
        
        // Kirim event ke Google Analytics jika tersedia
        if (typeof gtag !== 'undefined') {
            gtag('event', 'share', {
                'method': platform,
                'content_type': 'article',
                'item_id': window.location.pathname
            });
        }
        
        // Console log untuk debugging
        console.log('Share clicked:', platform);
    });
    
    // Optimasi untuk mobile - buka WhatsApp dengan app jika tersedia
    $('.share-btn.whatsapp').on('click', function(e) {
        if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            // Mobile device - coba buka app WhatsApp
            var url = $(this).attr('href');
            var text = url.split('text=')[1];
            
            // Format untuk app WhatsApp
            var whatsappUrl = 'whatsapp://send?text=' + text;
            
            // Coba buka app, fallback ke web jika gagal
            var startTime = Date.now();
            window.location = whatsappUrl;
            
            setTimeout(function() {
                if (Date.now() - startTime < 2000) {
                    // App tidak terbuka, gunakan web version
                    window.open(url, '_blank');
                }
            }, 1000);
            
            e.preventDefault();
        }
    });
    
    // Smooth scroll untuk mobile jika tombol share di bawah fold
    if (window.innerWidth <= 768) {
        $('.bawana-social-share').waypoint(function(direction) {
            if (direction === 'down') {
                // User scroll ke bagian share, mungkin tertarik untuk share
                console.log('User reached share section');
            }
        }, {
            offset: '100%'
        });
    }
    
});
