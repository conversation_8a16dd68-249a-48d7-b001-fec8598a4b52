<?php
/**
 * Template Name: Redaksi Page
 * Description: Halaman untuk menampilkan susunan redaksi Bawana News
 */

// Set page title to avoid undefined array key error
global $post;
if ($post) {
    $post->post_title = 'Redaksi';
}

get_header(); ?>

<div class="td-main-content-wrap td-container-wrap">
    <div class="td-container">
        <div class="td-crumb-container">
            <div class="td-crumb">
                <a href="<?php echo home_url(); ?>">Home</a>
                <span class="td-crumb-separator"> > </span>
                <span>Redaksi</span>
            </div>
        </div>

        <div class="td-pb-row">
            <div class="td-pb-span12">
                <div class="td-main-content">
                    <div class="td-ss-main-content">
                        <article class="post type-post status-publish format-standard">
                            <header class="td-post-title">
                                <h1 class="entry-title">Redaksi</h1>
                            </header>

                            <div class="td-post-content">
                                <div class="bawana-redaksi-page">
                                    <!-- Header -->
                                    <div class="redaksi-header">
                                        <h2>Dewan Redaksi</h2>
                                    </div>

                                    <!-- Vertical Organizational Structure -->
                                    <div class="redaksi-vertical">
                                        <!-- Pimpinan Perusahaan -->
                                        <div class="position-item">
                                            <div class="position-title">Pimpinan Perusahaan</div>
                                            <div class="person-name">Saeful Mu'minin</div>
                                        </div>

                                        <!-- Pimpinan Redaksi -->
                                        <div class="position-item">
                                            <div class="position-title">Pimpinan Redaksi</div>
                                            <div class="person-name">Babussalam</div>
                                        </div>

                                        <!-- Pimpinan Umum -->
                                        <div class="position-item">
                                            <div class="position-title">Pimpinan Umum</div>
                                            <div class="person-name">Darsono</div>
                                        </div>

                                        <!-- IT Support -->
                                        <div class="position-item">
                                            <div class="position-title">IT Support</div>
                                            <div class="person-name">Moh Mujiburrahman</div>
                                        </div>

                                        <!-- Redaktur -->
                                        <div class="position-item">
                                            <div class="position-title">Redaktur</div>
                                            <div class="person-name">Faizal Fardan, Kevin Fernando</div>
                                        </div>

                                        <!-- Manager Eksternal -->
                                        <div class="position-item">
                                            <div class="position-title">Manager Eksternal</div>
                                            <div class="person-name">Mukhammad Naufal</div>
                                        </div>

                                        <!-- Wakil Manager Eksternal -->
                                        <div class="position-item">
                                            <div class="position-title">Wakil Manager Eksternal</div>
                                            <div class="person-name">Abdurrahman Asy'ari</div>
                                        </div>

                                        <!-- Jurnalis -->
                                        <div class="position-item">
                                            <div class="position-title">Jurnalis</div>
                                            <div class="person-name">Mohammad Faidin, Abdul Khodir</div>
                                        </div>

                                        <!-- Wartawan -->
                                        <div class="position-item">
                                            <div class="position-title">Wartawan</div>
                                            <div class="person-name">M. Sholeh</div>
                                        </div>
                                    </div>

                                    <!-- Informasi Badan Hukum -->
                                    <div class="corporate-info-box" style="border-top: 2px solid #0A2E5B; padding-top: 20px; margin-top: 40px;">
                                        <h3 style="margin-top: 0;">Informasi Badan Hukum</h3>
                                        <p><strong>Nama Perusahaan:</strong> PT BAWANA MAGENTA SAKTI</p>
                                        <p><strong>SK Kemenkumham:</strong> AHU-0050892.AH.01.01.TAHUN 2025</p>
                                        <p><strong>Notaris:</strong> HELLY HERLAWATI S.H., M.KN.</p>
                                    </div>

                                    <!-- Contact Information -->
                                    <div class="redaksi-contact">
                                        <div class="contact-card">
                                            <h3>Alamat Redaksi</h3>
                                            <div class="contact-info">
                                                <p>
                                                    <strong>Jl. Arya Salingsingan No.76</strong><br>
                                                    Kasugengan Kidul, Kec. Depok<br>
                                                    Kabupaten Cirebon, Jawa Barat 45653
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bawana-redaksi-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.redaksi-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, var(--bawana-primary-blue), var(--bawana-accent-blue));
    color: var(--bawana-bg-primary);
    border-radius: 12px;
}

.redaksi-header h2 {
    font-size: 32px;
    margin: 0;
    color: var(--bawana-bg-primary);
}

.redaksi-vertical {
    max-width: 600px;
    margin: 0 auto;
}

.position-item {
    background: var(--bawana-bg-primary);
    border: 2px solid var(--bawana-border-color);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: var(--bawana-shadow-md);
    transition: all 0.3s ease;
    border-left: 5px solid var(--bawana-primary-blue);
}

.position-item:hover {
    transform: translateX(10px);
    box-shadow: var(--bawana-shadow-lg);
    border-left-color: var(--bawana-primary-gold);
    background: var(--bawana-bg-light);
}

.position-item:first-child {
    border-left-color: var(--bawana-primary-gold);
    background: linear-gradient(135deg, var(--bawana-bg-primary), #fff9e6);
}

.position-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--bawana-primary-blue);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.position-item:first-child .position-title {
    color: var(--bawana-primary-gold);
    font-size: 20px;
}

.person-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--bawana-text-secondary);
    padding: 10px 15px;
    background: var(--bawana-bg-light);
    border-radius: 8px;
    border: 1px solid var(--bawana-border-light);
}

.redaksi-legal-info {
    margin-top: 40px;
    margin-bottom: 30px;
}

.legal-card {
    background: linear-gradient(135deg, #f8f9ff, #fff9e6);
    border-radius: 12px;
    padding: 30px;
    border: 2px solid var(--bawana-primary-gold);
    box-shadow: var(--bawana-shadow-md);
}

.legal-card h3 {
    color: var(--bawana-primary-blue);
    font-size: 20px;
    margin-bottom: 20px;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.legal-content {
    display: grid;
    gap: 15px;
}

.legal-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: var(--bawana-bg-primary);
    border-radius: 8px;
    border-left: 4px solid var(--bawana-primary-gold);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.legal-item strong {
    color: var(--bawana-primary-blue);
    font-weight: 700;
    min-width: 140px;
}

.legal-item span {
    color: var(--bawana-text-secondary);
    font-weight: 600;
    text-align: right;
}

.redaksi-contact {
    margin-top: 40px;
}

.contact-card {
    background: var(--bawana-bg-light);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    border: 1px solid var(--bawana-border-color);
}

.contact-card h3 {
    color: var(--bawana-text-primary);
    font-size: 20px;
    margin-bottom: 20px;
    font-weight: 700;
}

.contact-info p {
    font-size: 16px;
    line-height: 1.8;
    color: var(--bawana-text-secondary);
    margin: 0;
}

.contact-info strong {
    color: var(--bawana-text-primary);
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bawana-redaksi-page {
        padding: 15px;
    }
    
    .redaksi-row {
        flex-direction: column;
        align-items: center;
    }
    
    .position-card {
        min-width: 100%;
        max-width: 400px;
    }
    
    .redaksi-header {
        padding: 20px;
    }
    
    .redaksi-header h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .position-card {
        padding: 20px;
        min-width: auto;
    }

    .contact-card, .legal-card {
        padding: 20px;
    }

    .legal-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .legal-item strong {
        min-width: auto;
    }

    .legal-item span {
        text-align: left;
    }
}
</style>

<?php get_footer(); ?>
