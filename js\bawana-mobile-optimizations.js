/**
 * Bawana News Mobile Optimizations
 * Optimasi JavaScript untuk pengalaman mobile yang lebih baik
 */

(function($) {
    'use strict';

    // Tunggu hingga DOM ready
    $(document).ready(function() {
        
        // Inisialisasi optimasi mobile
        BawanaMobileOptimizations.init();
        
    });

    var BawanaMobileOptimizations = {
        
        init: function() {
            this.setupResponsiveVideos();
            this.setupMobileNavigation();
            this.setupLazyLoading();
            this.setupTouchOptimizations();
            this.setupPerformanceOptimizations();
            // Header visibility removed - using theme's Header Builder instead
        },

        // Pastikan semua video responsive
        setupResponsiveVideos: function() {
            // Wrap semua iframe video yang belum di-wrap
            $('iframe[src*="youtube"], iframe[src*="facebook"], iframe[src*="vimeo"]').each(function() {
                var $iframe = $(this);
                
                if (!$iframe.parent().hasClass('bawana-video-container')) {
                    $iframe.wrap('<div class="bawana-video-container"></div>');
                }
                
                // Set responsive attributes
                $iframe.removeAttr('width height');
            });
        },

        // Optimasi navigasi mobile
        setupMobileNavigation: function() {
            if (window.innerWidth <= 767) {
                // Tambahkan smooth scrolling untuk mobile
                $('a[href^="#"]').on('click', function(e) {
                    var target = $(this.getAttribute('href'));
                    if (target.length) {
                        e.preventDefault();
                        $('html, body').animate({
                            scrollTop: target.offset().top - 80
                        }, 600);
                    }
                });

                // Auto-close mobile menu setelah klik link
                $('#td-mobile-nav a').on('click', function() {
                    setTimeout(function() {
                        $('body').removeClass('td-menu-mob-open-menu');
                    }, 300);
                });
            }
        },

        // Setup lazy loading untuk gambar dan iframe
        setupLazyLoading: function() {
            // Intersection Observer untuk lazy loading
            if ('IntersectionObserver' in window) {
                var imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            var img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.classList.remove('lazy');
                                imageObserver.unobserve(img);
                            }
                        }
                    });
                });

                // Observe semua gambar dengan class lazy
                $('.lazy').each(function() {
                    imageObserver.observe(this);
                });
            }
        },

        // Optimasi untuk touch devices
        setupTouchOptimizations: function() {
            if ('ontouchstart' in window) {
                // Tambahkan class untuk touch device
                $('body').addClass('touch-device');

                // Improve touch targets
                $('.popular-post-item, .widget a').css({
                    'min-height': '44px',
                    'min-width': '44px'
                });

                // Hapus hover effects pada touch devices
                $('.popular-post-item').off('mouseenter mouseleave');
            }
        },

        // Optimasi performance
        setupPerformanceOptimizations: function() {
            // Debounce resize events
            var resizeTimer;
            $(window).on('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(function() {
                    BawanaMobileOptimizations.handleResize();
                }, 250);
            });

            // Throttle scroll events
            var scrollTimer;
            var isScrolling = false;
            $(window).on('scroll', function() {
                if (!isScrolling) {
                    window.requestAnimationFrame(function() {
                        BawanaMobileOptimizations.handleScroll();
                        isScrolling = false;
                    });
                    isScrolling = true;
                }
            });
        },

        // Handle window resize
        handleResize: function() {
            var windowWidth = $(window).width();
            
            // Adjust video containers berdasarkan ukuran layar
            if (windowWidth <= 480) {
                $('.bawana-video-container').css('padding-bottom', '65%');
            } else if (windowWidth <= 767) {
                $('.bawana-video-container').css('padding-bottom', '60%');
            } else {
                $('.bawana-video-container').css('padding-bottom', '56.25%');
            }

            // Toggle widget visibility berdasarkan ukuran layar
            if (windowWidth <= 480) {
                $('.widget.bawana_talkshow').hide();
            } else {
                $('.widget.bawana_talkshow').show();
            }
        },

        // Handle scroll events
        handleScroll: function() {
            var scrollTop = $(window).scrollTop();

            // Add/remove scrolled class untuk styling
            if (scrollTop > 100) {
                $('body').addClass('scrolled');
            } else {
                $('body').removeClass('scrolled');
            }
        },

        // Header visibility management removed - using theme's Header Builder instead
        // This prevents conflicts and ensures stable mobile header display
    };

    // Expose ke global scope jika diperlukan
    window.BawanaMobileOptimizations = BawanaMobileOptimizations;

})(jQuery);

// Vanilla JS optimizations yang tidak memerlukan jQuery
document.addEventListener('DOMContentLoaded', function() {
    
    // Preload critical resources
    var preloadLinks = [
        'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
    ];
    
    preloadLinks.forEach(function(href) {
        var link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        document.head.appendChild(link);
    });

    // Service Worker removed - file not available

    // Optimize images dengan Intersection Observer
    if ('IntersectionObserver' in window) {
        var imageObserver = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var img = entry.target;
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img').forEach(function(img) {
            imageObserver.observe(img);
        });
    }
});

// Critical performance optimizations
(function() {
    // Reduce layout thrashing
    var raf = window.requestAnimationFrame || window.setTimeout;
    
    // Optimize scroll performance
    var scrolling = false;
    window.addEventListener('scroll', function() {
        if (!scrolling) {
            raf(function() {
                // Scroll optimizations here
                scrolling = false;
            });
            scrolling = true;
        }
    }, { passive: true });
})();
