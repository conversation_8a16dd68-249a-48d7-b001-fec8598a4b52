<?php 
get_header();
global $content_width;

// Cek setting sidebar untuk post ini
$sidebar_setting = get_post_meta(get_the_ID(), '_bawana_sidebar_setting', true);

// Tentukan apakah menggunakan sidebar atau tidak
$use_sidebar = true; // Default menggunakan sidebar

if ($sidebar_setting === 'no_sidebar') {
    $use_sidebar = false;
} elseif ($sidebar_setting === 'with_sidebar') {
    $use_sidebar = true;
} else {
    // Default: gunakan sidebar untuk single post (berbeda dari tema asli)
    $use_sidebar = true;
}

// Set content width berdasarkan layout
if ($use_sidebar) {
    $content_width = 696; // Width dengan sidebar
    $content_class = 'td-pb-span8';
} else {
    $content_width = 1068; // Full width
    $content_class = 'td-pb-span12';
}
?>

<div class="td-main-content-wrap td-container-wrap">
    <div class="td-container">
        <div class="td-crumb-container">
            <?php echo tagdiv_page_generator::get_breadcrumbs(array(
                'template' => 'single',
                'title' => get_the_title(),
            )); ?>
        </div>

        <div class="td-pb-row">
            <div class="<?php echo esc_attr($content_class); ?> td-main-content">
                <div class="td-ss-main-content">
                    <?php
                    get_template_part('loop-single');
                    // Komentar dinonaktifkan untuk Bawana News
                    // comments_template('', true);
                    ?>
                </div>
            </div>

            <?php if ($use_sidebar): ?>
            <div class="td-pb-span4 td-main-sidebar">
                <div class="td-ss-main-sidebar">
                    <?php dynamic_sidebar('td-default'); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php get_footer(); ?>
