<?php
/**
 * Template Name: Tentan<PERSON> Page
 * Description: Halaman untuk menampilkan informasi tentang Bawana News
 */

// Set page title to avoid undefined array key error
global $post;
if ($post) {
    $post->post_title = 'Tentang Ka<PERSON>';
}

get_header(); ?>

<div class="td-main-content-wrap td-container-wrap">
    <div class="td-container">
        <div class="td-crumb-container">
            <div class="td-crumb">
                <a href="<?php echo home_url(); ?>">Home</a>
                <span class="td-crumb-separator"> > </span>
                <span>Tentang <PERSON>mi</span>
            </div>
        </div>

        <div class="td-pb-row">
            <div class="td-pb-span12">
                <div class="td-main-content">
                    <div class="td-ss-main-content">
                        <article class="post type-post status-publish format-standard">
                            <header class="td-post-title">
                                <h1 class="entry-title">Tentang <PERSON></h1>
                            </header>

                            <div class="td-post-content">
                                <div class="bawana-tentang-page">
                                    <!-- Hero Section -->
                                    <div class="tentang-hero">
                                        <div class="hero-content">
                                            <h2>Bawana News</h2>
                                            <p class="hero-tagline">Mendunia</p>
                                            <div class="hero-description">
                                                <p>Portal berita digital terpercaya yang menghadirkan informasi akurat, terkini, dan berimbang untuk masyarakat Indonesia dan dunia.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Narasi Legalitas Perusahaan -->
                                    <div class="tentang-legal-narrative">
                                        <div class="narrative-content">
                                            <p class="narrative-intro">
                                                <strong>PT BAWANA MAGENTA SAKTI</strong> adalah perusahaan media digital yang didirikan pada tahun 2024 dengan komitmen tinggi untuk menyajikan jurnalisme berkualitas. Sebagai badan hukum Perseroan Terbatas yang bergerak di bidang media digital dan jurnalistik, kami beroperasi dengan standar profesional yang tinggi dan mematuhi seluruh regulasi yang berlaku di Indonesia.
                                            </p>
                                            
                                            <p class="narrative-mission">
                                                Dengan legalitas yang jelas dan struktur organisasi yang solid, PT BAWANA MAGENTA SAKTI berkomitmen untuk menjadi platform media yang dapat dipercaya masyarakat. Kami mengutamakan akurasi, objektivitas, dan integritas dalam setiap pemberitaan yang kami sajikan.
                                            </p>
                                            
                                            <p class="narrative-vision">
                                                Visi kami adalah menjadi media digital terdepan yang menghubungkan Indonesia dengan dunia melalui informasi berkualitas tinggi. Melalui platform Bawana News, kami berupaya memberikan kontribusi positif bagi kemajuan bangsa dan peradaban global.
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Visi Misi -->
                                    <div class="tentang-visi-misi">
                                        <div class="vm-grid">
                                            <div class="vm-card visi-card">
                                                <h3>Visi</h3>
                                                <p>Menjadi platform media digital terpercaya yang menghubungkan Indonesia dengan dunia melalui jurnalisme berkualitas tinggi.</p>
                                            </div>
                                            <div class="vm-card misi-card">
                                                <h3>Misi</h3>
                                                <ul>
                                                    <li>Menyajikan berita akurat dan berimbang</li>
                                                    <li>Mengutamakan kepentingan publik</li>
                                                    <li>Menjunjung tinggi etika jurnalistik</li>
                                                    <li>Memberikan edukasi kepada masyarakat</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Nilai-Nilai -->
                                    <div class="tentang-values">
                                        <h3>Nilai-Nilai Kami</h3>
                                        <div class="values-grid">
                                            <div class="value-item">
                                                <div class="value-icon">📰</div>
                                                <h4>Akurasi</h4>
                                                <p>Mengutamakan kebenaran dan ketepatan informasi</p>
                                            </div>
                                            <div class="value-item">
                                                <div class="value-icon">⚖️</div>
                                                <h4>Objektivitas</h4>
                                                <p>Menyajikan berita secara berimbang dan tidak memihak</p>
                                            </div>
                                            <div class="value-item">
                                                <div class="value-icon">🤝</div>
                                                <h4>Integritas</h4>
                                                <p>Menjunjung tinggi kejujuran dan transparansi</p>
                                            </div>
                                            <div class="value-item">
                                                <div class="value-icon">🌍</div>
                                                <h4>Global</h4>
                                                <p>Menghubungkan Indonesia dengan dunia</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Contact CTA -->
                                    <div class="tentang-contact-cta">
                                        <div class="cta-content">
                                            <h3>Hubungi Kami</h3>
                                            <p>Untuk informasi lebih lanjut, kritik, saran, atau kerjasama, silakan hubungi redaksi kami.</p>
                                            <a href="<?php echo home_url('/kontak'); ?>" class="cta-button">Kontak Redaksi</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bawana-tentang-page {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.tentang-hero {
    text-align: center;
    margin-bottom: 50px;
    padding: 50px 30px;
    background: linear-gradient(135deg, var(--bawana-primary-blue), var(--bawana-accent-blue));
    color: var(--bawana-bg-primary);
    border-radius: 16px;
    box-shadow: var(--bawana-shadow-lg);
}

.tentang-hero h2 {
    font-size: 42px;
    margin: 0 0 10px 0;
    font-weight: 800;
    color: var(--bawana-bg-primary);
}

.hero-tagline {
    font-size: 20px;
    color: var(--bawana-primary-gold);
    font-weight: 600;
    margin-bottom: 25px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.hero-description p {
    font-size: 18px;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.95);
    max-width: 600px;
    margin: 0 auto;
}

.tentang-legal-narrative {
    margin-bottom: 50px;
    padding: 40px;
    background: linear-gradient(135deg, #f8f9ff, #fff9e6);
    border-radius: 16px;
    border: 2px solid var(--bawana-primary-gold);
    box-shadow: var(--bawana-shadow-md);
}

.narrative-content p {
    font-size: 16px;
    line-height: 1.8;
    color: var(--bawana-text-secondary);
    margin-bottom: 20px;
    text-align: justify;
}

.narrative-intro {
    font-size: 17px !important;
    font-weight: 500;
}

.narrative-intro strong {
    color: var(--bawana-primary-blue);
    font-weight: 700;
}

.tentang-visi-misi {
    margin-bottom: 50px;
}

.vm-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.vm-card {
    background: var(--bawana-bg-primary);
    border-radius: 12px;
    padding: 30px;
    border: 2px solid var(--bawana-border-color);
    box-shadow: var(--bawana-shadow-md);
    transition: all 0.3s ease;
}

.vm-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--bawana-shadow-lg);
}

.visi-card {
    border-left: 5px solid var(--bawana-primary-blue);
}

.misi-card {
    border-left: 5px solid var(--bawana-primary-gold);
}

.vm-card h3 {
    color: var(--bawana-primary-blue);
    font-size: 22px;
    margin-bottom: 15px;
    font-weight: 700;
}

.vm-card p, .vm-card li {
    color: var(--bawana-text-secondary);
    line-height: 1.7;
    font-size: 16px;
}

.vm-card ul {
    padding-left: 20px;
    margin: 0;
}

.vm-card li {
    margin-bottom: 8px;
}

.tentang-values {
    margin-bottom: 50px;
}

.tentang-values h3 {
    text-align: center;
    color: var(--bawana-primary-blue);
    font-size: 28px;
    margin-bottom: 40px;
    font-weight: 700;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
}

.value-item {
    text-align: center;
    background: var(--bawana-bg-primary);
    border-radius: 12px;
    padding: 30px 20px;
    border: 1px solid var(--bawana-border-color);
    box-shadow: var(--bawana-shadow-sm);
    transition: all 0.3s ease;
}

.value-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--bawana-shadow-md);
    border-color: var(--bawana-primary-gold);
}

.value-icon {
    font-size: 40px;
    margin-bottom: 15px;
}

.value-item h4 {
    color: var(--bawana-primary-blue);
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 700;
}

.value-item p {
    color: var(--bawana-text-secondary);
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
}

.tentang-contact-cta {
    text-align: center;
    padding: 40px;
    background: var(--bawana-bg-light);
    border-radius: 12px;
    border: 1px solid var(--bawana-border-color);
}

.cta-content h3 {
    color: var(--bawana-primary-blue);
    font-size: 24px;
    margin-bottom: 15px;
    font-weight: 700;
}

.cta-content p {
    color: var(--bawana-text-secondary);
    font-size: 16px;
    margin-bottom: 25px;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, var(--bawana-primary-blue), var(--bawana-accent-blue));
    color: var(--bawana-bg-primary);
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: var(--bawana-shadow-sm);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--bawana-shadow-md);
    color: var(--bawana-bg-primary);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bawana-tentang-page {
        padding: 15px;
    }
    
    .tentang-hero {
        padding: 30px 20px;
    }
    
    .tentang-hero h2 {
        font-size: 32px;
    }
    
    .hero-tagline {
        font-size: 16px;
    }
    
    .hero-description p {
        font-size: 16px;
    }
    
    .vm-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }
    
    .tentang-legal-narrative {
        padding: 25px;
    }
}

@media (max-width: 480px) {
    .tentang-hero h2 {
        font-size: 28px;
    }
    
    .vm-card, .tentang-contact-cta {
        padding: 20px;
    }
    
    .value-item {
        padding: 20px 15px;
    }
    
    .values-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
