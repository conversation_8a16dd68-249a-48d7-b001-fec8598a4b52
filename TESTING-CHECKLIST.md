# 🧪 Testing Checklist - Bawana News

## 📋 Daftar Testing yang Harus Dilakukan

### ✅ 1. Testing Instalasi Child Theme

- [ ] **Upload child theme** ke `/wp-content/themes/`
- [ ] **Aktivasi theme** di Dashboard → Appearance → Themes
- [ ] **Cek apakah logo "Bawana News"** muncul di header
- [ ] **Cek warna biru dan emas** sudah sesuai branding
- [ ] **Cek footer** menampilkan copyright Bawana News

**Expected Result**: Theme aktif dengan branding Bawana News

---

### ✅ 2. Testing Video Sidebar Management

#### Test Live Streaming:
- [ ] **Masuk ke Dashboard** → Newspaper → Video Sidebar
- [ ] **Input kode embed YouTube** di bagian Live Streaming
- [ ] **Klik Simpan Perubahan**
- [ ] **Cek frontend** - video muncul di sidebar dengan judul "LIVE STREAMING"
- [ ] **Test responsive** - video menyesuaikan ukuran layar

#### Test Talkshow:
- [ ] **Input kode embed YouTube** di bagian Talkshow
- [ ] **Klik Simpan <PERSON>bah<PERSON>**
- [ ] **Cek frontend** - video muncul di sidebar dengan judul "TALKSHOW"
- [ ] **Test responsive** - video menyesuaikan ukuran layar

**Expected Result**: Video embed berfungsi dan tampil responsif

---

### ✅ 3. Testing Popular Posts Widget

- [ ] **Buat 5-10 post dummy** dengan featured image
- [ ] **Tambahkan beberapa komentar** di post-post tersebut
- [ ] **Cek sidebar** - widget "BERITA POPULER" muncul
- [ ] **Verifikasi urutan** - post dengan komentar terbanyak di atas
- [ ] **Cek tampilan**:
  - [ ] Nomor urutan (1,2,3,4,5) dengan background biru
  - [ ] Thumbnail gambar muncul
  - [ ] Judul berita terpotong dengan baik
  - [ ] Tanggal dan jumlah komentar muncul
- [ ] **Test hover effect** - gambar sedikit membesar saat di-hover

**Expected Result**: Widget menampilkan 5 berita populer otomatis

---

### ✅ 4. Testing Sidebar Layout

- [ ] **Cek urutan widget** di sidebar:
  1. Live Streaming (paling atas)
  2. Berita Populer (tengah)
  3. Talkshow (paling bawah)
- [ ] **Cek spacing** antar widget
- [ ] **Cek responsive** di mobile dan tablet
- [ ] **Cek warna header** setiap widget sesuai branding

**Expected Result**: Layout sidebar profesional dan rapi

---

### ✅ 5. Testing Simplified Editor Interface

#### Test dengan User Non-Admin:
- [ ] **Buat user baru** dengan role "Editor" atau "Author"
- [ ] **Login dengan user tersebut**
- [ ] **Masuk ke Posts → Add New**
- [ ] **Verifikasi yang TERSEMBUNYI**:
  - [ ] Custom Fields
  - [ ] Excerpt
  - [ ] Trackbacks
  - [ ] Discussion
  - [ ] Slug
  - [ ] Tags
- [ ] **Verifikasi yang TERLIHAT**:
  - [ ] Title dengan label "📝 JUDUL BERITA"
  - [ ] Content Editor
  - [ ] Categories dengan icon "📂"
  - [ ] Featured Image dengan icon "🖼️"
  - [ ] Publish button berwarna biru besar

**Expected Result**: Interface sederhana dan user-friendly

---

### ✅ 6. Testing Admin Menu Simplification

#### Test dengan User Non-Admin:
- [ ] **Login sebagai Editor/Author**
- [ ] **Verifikasi menu yang TERSEMBUNYI**:
  - [ ] Tools
  - [ ] Settings
  - [ ] Themes
  - [ ] Plugins
  - [ ] Users
  - [ ] Pages
- [ ] **Verifikasi menu yang TERLIHAT**:
  - [ ] Dashboard
  - [ ] Posts
  - [ ] Media
  - [ ] Comments
  - [ ] Newspaper (Theme Panel)

**Expected Result**: Menu admin disederhanakan untuk operator

---

### ✅ 7. Testing Content Creation Workflow

#### Test Membuat Berita:
- [ ] **Login sebagai operator** (non-admin user)
- [ ] **Buat post baru** dengan langkah:
  1. Tulis judul di kotak biru
  2. Tulis konten di editor
  3. Pilih kategori
  4. Upload featured image
  5. Klik Publish
- [ ] **Cek hasil di frontend**:
  - [ ] Post muncul di homepage
  - [ ] Featured image tampil
  - [ ] Kategori benar
  - [ ] Tanggal publikasi benar

**Expected Result**: Workflow pembuatan berita mudah dan intuitif

---

### ✅ 8. Testing Performance & Compatibility

- [ ] **Test loading speed** - website load dalam < 3 detik
- [ ] **Test di berbagai browser**:
  - [ ] Chrome
  - [ ] Firefox
  - [ ] Safari
  - [ ] Edge
- [ ] **Test responsive design**:
  - [ ] Desktop (1920px)
  - [ ] Tablet (768px)
  - [ ] Mobile (375px)
- [ ] **Test dengan konten banyak** - buat 20+ posts
- [ ] **Cek error di Console** browser

**Expected Result**: Website cepat, responsive, dan bebas error

---

### ✅ 9. Testing Security & Permissions

- [ ] **Test akses unauthorized**:
  - [ ] User Editor tidak bisa akses Theme Settings
  - [ ] User Author tidak bisa edit post orang lain
  - [ ] Guest tidak bisa akses admin area
- [ ] **Test nonce security** di form video sidebar
- [ ] **Test data sanitization** - input script tidak dieksekusi

**Expected Result**: Keamanan terjaga sesuai role user

---

### ✅ 10. Testing Documentation

- [ ] **Baca PANDUAN-OPERATOR.md** - pastikan mudah dipahami
- [ ] **Test setiap langkah** di panduan
- [ ] **Verifikasi screenshot** (jika ada) masih akurat
- [ ] **Test dengan orang non-teknis** - minta feedback

**Expected Result**: Panduan mudah diikuti oleh operator awam

---

## 🚨 Critical Issues to Watch

### ⚠️ Red Flags:
- Video tidak muncul di sidebar
- Popular posts tidak update otomatis
- Layout broken di mobile
- User bisa akses menu yang seharusnya hidden
- Error 500 saat save video embed

### 🔧 Quick Fixes:
- Clear cache browser dan server
- Deactivate/reactivate theme
- Check file permissions (755 for folders, 644 for files)
- Verify WordPress version compatibility

---

## 📊 Testing Report Template

```
TESTING COMPLETED: [Date]
TESTER: [Name]
WORDPRESS VERSION: [Version]
THEME VERSION: Newspaper Child v1.0

RESULTS:
✅ Passed: [X/10]
❌ Failed: [X/10]
⚠️ Issues: [List any issues]

RECOMMENDATIONS:
[Any suggestions for improvement]

READY FOR PRODUCTION: [YES/NO]
```

---

**🎯 Goal**: Semua checklist harus ✅ sebelum website diserahkan ke operator!
