<?php
// SECURITY: Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

do_action( 'tdc_header' );
if ( has_action( 'tdc_header' ) ) {
    return;
}
?>
<!doctype html >
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' );?>" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="pingback" href="<?php bloginfo( 'pingback_url' ); ?>" />
    <?php wp_head(); ?>
</head>

<body <?php body_class('tagdiv-small-theme') ?> itemscope="itemscope" itemtype="https://schema.org/WebPage">
<?php wp_body_open() ?>

    <!-- Mobile Search -->
    <div class="td-search-background" style="visibility:hidden"></div>
    <div class="td-search-wrap-mob" style="visibility:hidden">
        <div class="td-drop-down-search" aria-labelledby="td-header-search-button">
            <form method="get" class="td-search-form" action="<?php echo esc_url( home_url( '/' ) ); ?>">
                <div class="td-search-close">
                    <a href="#"><i class="td-icon-close-mobile"></i></a>
                </div>
                <div role="search" class="td-search-input">
                    <span><?php esc_attr_e('Search', 'newspaper' )?></span>
                    <label for="td-header-search-mob">
                        <input id="td-header-search-mob" type="text" value="<?php echo get_search_query(); ?>" name="s" autocomplete="off" />
                    </label>
                </div>
            </form>
            <div id="td-aj-search-mob"></div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div class="td-menu-background" style="visibility:hidden"></div>
    <div id="td-mobile-nav" style="visibility:hidden">
        <div class="td-mobile-container">
            <!-- mobile menu top section -->
            <div class="td-menu-socials-wrap">
                <!-- close button -->
                <div class="td-mobile-close">
                    <a href="#"><i class="td-icon-close-mobile"></i></a>
                </div>
            </div>

            <!-- menu section -->
            <div class="td-mobile-content">
                <?php
                    wp_nav_menu(array(
                        'theme_location' => 'header-menu',
                        'menu_class'=> 'td-mobile-main-menu',
                        'fallback_cb' => 'tagdiv_wp_no_mobile_menu',
                        'link_after' => '<i class="td-icon-menu-right td-element-after"></i>'
                    ));

                // if no menu
                function tagdiv_wp_no_mobile_menu() {
                    if ( current_user_can( 'switch_themes' ) ) {
                        echo '<ul class="">';
                        echo '<li class="menu-item-first"><a href="' . esc_url(home_url('/')) . 'wp-admin/nav-menus.php?action=locations">Add menu</a></li>';
                        echo '</ul>';
                    }
                }
                ?>
            </div>
        </div>
    </div>

    <div id="td-outer-wrap" class="td-theme-wrap">
        <div class="td-header-wrap td-header-style-1">
            <div class="td-banner-wrap-full td-logo-wrap-full td-container-wrap">
                <div class="td-header-sp-logo">
                    <!-- TRAVEL BIRDIE STYLE - MENU ABOVE, LOGO BELOW -->
                    <div class="bawana-header-structure">
                        <!-- Top Navigation Menu -->
                        <div class="bawana-top-menu">
                            <!-- Mobile Toggle Button -->
                            <div id="td-top-mobile-toggle">
                                <a href="#" role="button" aria-label="mobile-toggle">
                                    <i class="td-icon-font td-icon-mobile"></i>
                                </a>
                            </div>

                            <?php
                            wp_nav_menu(array(
                                'theme_location' => 'header-menu',
                                'menu_class' => 'bawana-main-menu',
                                'container' => false,
                                'fallback_cb' => false
                            ));

                            // Fallback menu if no menu is set
                            if (!has_nav_menu('header-menu')) {
                                echo '<ul class="bawana-main-menu">';
                                echo '<li><a href="' . esc_url(home_url('/')) . '">HOME</a></li>';
                                echo '<li><a href="#">NASIONAL</a></li>';
                                echo '<li><a href="#">INTERNASIONAL</a></li>';
                                echo '<li><a href="#">OLAHRAGA</a></li>';
                                echo '<li><a href="#">EKONOMI</a></li>';
                                echo '<li><a href="#">TEKNOLOGI</a></li>';
                                echo '</ul>';
                            }
                            ?>
                        </div>

                        <!-- Logo Section -->
                        <div class="bawana-logo-section">
                            <?php
                            if (has_custom_logo()) {
                                the_custom_logo();
                            } else {
                                // Fallback to Bawana logo if no custom logo set
                                echo '<img src="' . get_stylesheet_directory_uri() . '/images/bawana-logo.png" alt="' . get_bloginfo('name') . '" class="bawana-fallback-logo" />';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
