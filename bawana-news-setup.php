<?php
/**
 * Bawana News Setup Script
 * Script untuk setup awal website Bawana News
 *
 * SECURITY: Prevent direct access
 */
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Jalankan file ini sekali setelah mengaktifkan child theme
 */

// Pastikan file ini hanya bisa diakses dari WordPress
if (!defined('ABSPATH')) {
    // Jika diakses langsung, redirect ke WordPress
    require_once('../../../wp-load.php');
}

// Cek apakah user memiliki permission
if (!current_user_can('manage_options')) {
    wp_die('Anda tidak memiliki permission untuk menjalankan script ini.');
}

echo '<h1>Bawana News Setup</h1>';
echo '<p>Memulai setup website Bawana News...</p>';

// 1. Upload logo Bawana News
function bawana_upload_logo() {
    // Cek apakah logo sudah ada
    $custom_logo_id = get_theme_mod('custom_logo');
    if ($custom_logo_id) {
        echo '<p>✓ Logo sudah ada</p>';
        return;
    }
    
    // Path ke logo (sesuaikan dengan lokasi file logo Anda)
    $logo_path = get_stylesheet_directory() . '/assets/bawana-logo.png';
    
    if (!file_exists($logo_path)) {
        echo '<p>⚠ File logo tidak ditemukan di: ' . $logo_path . '</p>';
        echo '<p>Silakan upload logo secara manual melalui Customizer > Site Identity</p>';
        return;
    }
    
    // Upload logo ke media library
    $upload_dir = wp_upload_dir();
    $image_data = file_get_contents($logo_path);
    $filename = 'bawana-news-logo.png';
    
    if (wp_mkdir_p($upload_dir['path'])) {
        $file = $upload_dir['path'] . '/' . $filename;
    } else {
        $file = $upload_dir['basedir'] . '/' . $filename;
    }
    
    file_put_contents($file, $image_data);
    
    $wp_filetype = wp_check_filetype($filename, null);
    $attachment = array(
        'post_mime_type' => $wp_filetype['type'],
        'post_title' => sanitize_file_name($filename),
        'post_content' => '',
        'post_status' => 'inherit'
    );
    
    $attach_id = wp_insert_attachment($attachment, $file);
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    $attach_data = wp_generate_attachment_metadata($attach_id, $file);
    wp_update_attachment_metadata($attach_id, $attach_data);
    
    // Set sebagai custom logo
    set_theme_mod('custom_logo', $attach_id);
    
    echo '<p>✓ Logo berhasil diupload dan diset</p>';
}

// 2. Verifikasi halaman yang dibuat
function bawana_verify_pages() {
    $pages_to_check = array('redaksi', 'kode-etik-jurnalistik', 'kontak', 'tentang-kami', 'pedoman-media-siber', 'advertise');
    
    echo '<h3>Verifikasi Halaman:</h3>';
    foreach ($pages_to_check as $slug) {
        $page = get_page_by_path($slug);
        if ($page) {
            echo '<p>✓ Halaman "' . $page->post_title . '" tersedia - <a href="' . get_permalink($page->ID) . '" target="_blank">Lihat</a></p>';
        } else {
            echo '<p>✗ Halaman dengan slug "' . $slug . '" tidak ditemukan</p>';
        }
    }
}

// 3. Verifikasi menu
function bawana_verify_menus() {
    echo '<h3>Verifikasi Menu:</h3>';
    
    // Cek Header Menu
    $header_menu = wp_get_nav_menu_object('Header Utama');
    if ($header_menu) {
        echo '<p>✓ Menu "Header Utama" tersedia dengan ' . wp_get_nav_menu_items($header_menu->term_id) ? count(wp_get_nav_menu_items($header_menu->term_id)) : 0 . ' item</p>';
    } else {
        echo '<p>✗ Menu "Header Utama" tidak ditemukan</p>';
    }
    
    // Cek Footer Menu
    $footer_menu = wp_get_nav_menu_object('Footer Informasi');
    if ($footer_menu) {
        echo '<p>✓ Menu "Footer Informasi" tersedia dengan ' . wp_get_nav_menu_items($footer_menu->term_id) ? count(wp_get_nav_menu_items($footer_menu->term_id)) : 0 . ' item</p>';
    } else {
        echo '<p>✗ Menu "Footer Informasi" tidak ditemukan</p>';
    }
    
    // Cek menu locations
    $locations = get_theme_mod('nav_menu_locations');
    if (isset($locations['header-menu'])) {
        echo '<p>✓ Header menu location assigned</p>';
    } else {
        echo '<p>✗ Header menu location tidak assigned</p>';
    }
    
    if (isset($locations['footer-menu'])) {
        echo '<p>✓ Footer menu location assigned</p>';
    } else {
        echo '<p>✗ Footer menu location tidak assigned</p>';
    }
}

// 4. Verifikasi site settings
function bawana_verify_site_settings() {
    echo '<h3>Verifikasi Site Settings:</h3>';
    
    $site_title = get_bloginfo('name');
    $site_tagline = get_bloginfo('description');
    
    echo '<p>Site Title: <strong>' . $site_title . '</strong></p>';
    echo '<p>Site Tagline: <strong>' . $site_tagline . '</strong></p>';
    
    if ($site_title === 'Bawana News') {
        echo '<p>✓ Site title sudah benar</p>';
    } else {
        echo '<p>⚠ Site title belum diupdate ke "Bawana News"</p>';
    }
    
    if ($site_tagline === 'Mendunia') {
        echo '<p>✓ Site tagline sudah benar</p>';
    } else {
        echo '<p>⚠ Site tagline belum diupdate ke "Mendunia"</p>';
    }
}

// Jalankan setup jika diminta - ENHANCED SECURITY
if (isset($_GET['run_setup']) && $_GET['run_setup'] === 'true') {
    // SECURITY: Validate nonce and capability
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized access');
    }

    if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'bawana_setup_nonce')) {
        wp_die('Security check failed');
    }

    echo '<h2>Menjalankan Setup...</h2>';

    // Trigger setup function
    do_action('after_setup_theme');

    echo '<p>✓ Setup function triggered</p>';
    
    // Upload logo
    bawana_upload_logo();
    
    echo '<h2>Setup Selesai!</h2>';
    echo '<p><a href="?run_setup=false">Refresh untuk melihat hasil</a></p>';
} else {
    // Tampilkan status saat ini
    echo '<h2>Status Setup Saat Ini:</h2>';
    
    bawana_verify_site_settings();
    bawana_verify_pages();
    bawana_verify_menus();
    
    $setup_done = get_option('bawana_news_setup_done');
    if ($setup_done) {
        echo '<p style="color: green; font-weight: bold;">✓ Setup sudah pernah dijalankan</p>';
    } else {
        echo '<p style="color: orange; font-weight: bold;">⚠ Setup belum dijalankan</p>';
        $setup_url = wp_nonce_url('?run_setup=true', 'bawana_setup_nonce');
        echo '<p><a href="' . esc_url($setup_url) . '" style="background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;">Jalankan Setup</a></p>';
    }
}

echo '<hr>';
echo '<h3>Langkah Selanjutnya:</h3>';
echo '<ul>';
echo '<li>Buka <a href="' . admin_url('customize.php') . '" target="_blank">Customizer</a> untuk upload logo Bawana News</li>';
echo '<li>Buka <a href="' . admin_url('nav-menus.php') . '" target="_blank">Menu Management</a> untuk melihat menu yang dibuat</li>';
echo '<li>Buka <a href="' . admin_url('edit.php?post_type=page') . '" target="_blank">Pages</a> untuk melihat halaman yang dibuat</li>';
echo '<li>Kunjungi <a href="' . home_url() . '" target="_blank">Homepage</a> untuk melihat hasil</li>';
echo '</ul>';
?>
