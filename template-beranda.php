<?php 
/* Template Name: Beranda Kustom Bawana */ 
get_header(); 
?>

<div class="bawana-homepage-content">

    <?php
    // Debug info removed for production - clean frontend display
    ?>

    <!-- SECTIONS WITH DYNAMIC ORDER SUPPORT -->
    <?php
    // Check if user wants to use new section order system
    $use_section_order = get_option('bawana_use_section_order', 0);

    if ($use_section_order && function_exists('bawana_render_section')) {
        // NEW: Dynamic section order
        $section_order = get_option('bawana_section_order', 'hot_news,popular_articles,featured_posts,video_section,latest_stories');
        $sections = explode(',', $section_order);

        echo '<div class="bawana-dynamic-sections">';
        foreach ($sections as $section) {
            $section = trim($section);
            bawana_render_section($section);
        }
        echo '</div>';
    } else {
        // EXISTING: Keep original structure for compatibility
    ?>

    <!-- BAGIAN 1: BERITA PANAS (HOT NEWS) - DIATUR ADMIN -->
    <?php if (get_option('bawana_show_hot_news', 1)) : ?>
    <section class="bawana-section bawana-hot-news">
        <div class="section-header">
            <h2 class="section-title"><?php echo esc_html(get_option('bawana_hot_news_title', 'Berita Terpanas')); ?></h2>
            <?php if (current_user_can('manage_options')) : ?>
                <a href="<?php echo admin_url('admin.php?page=bawana-hot-news'); ?>" class="admin-edit-link">✏️ Edit Berita Terpanas</a>
            <?php endif; ?>
        </div>
        <div class="bawana-grid-5">
            <?php
            // Get admin-selected hot news posts
            $hot_news_posts = get_option('bawana_hot_news_posts', array());

            // Debug info for admin (only visible in HTML source)
            if (current_user_can('manage_options')) {
                echo '<!-- Bawana Debug: Hot News Posts = ' . print_r($hot_news_posts, true) . ' -->';
                echo '<!-- Bawana Debug: Admin URL = ' . admin_url('admin.php?page=bawana-hot-news') . ' -->';
            }

            if (!empty($hot_news_posts)) {
                // Show admin-selected posts
                $hot_query = new WP_Query(array(
                    'post__in' => $hot_news_posts,
                    'orderby' => 'post__in',
                    'posts_per_page' => 5,
                    'post_status' => 'publish'
                ));
            } else {
                // Fallback to latest posts if no admin selection
                $hot_query = new WP_Query(array(
                    'posts_per_page' => 5,
                    'ignore_sticky_posts' => 1,
                    'post_status' => 'publish',
                    'orderby' => 'date',
                    'order' => 'DESC'
                ));
            }

            if ($hot_query->have_posts()) :
                while ($hot_query->have_posts()) : $hot_query->the_post();
                    // Struktur HTML untuk setiap item berita panas
                    echo '<div class="grid-item">';
                    echo '<a href="' . get_the_permalink() . '" class="item-image">';
                    echo bawana_get_post_thumbnail(get_the_ID(), 'medium', 'hot-news-thumb');
                    echo '</a>';
                    echo '<div class="item-content">';
                    $category = get_the_category();
                    if ($category) {
                        echo '<a class="item-category" href="' . get_category_link($category[0]->term_id) . '">' . $category[0]->name . '</a>';
                    }
                    echo '<h3 class="item-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';
                    echo '</div></div>';
                endwhile;
            else :
                // Fallback jika belum ada postingan
                for ($i = 1; $i <= 5; $i++) {
                    echo '<div class="grid-item placeholder">';
                    echo '<div class="item-content">';
                    echo '<span class="item-category">KATEGORI</span>';
                    echo '<h3 class="item-title">Belum ada berita. Silakan tambahkan postingan baru.</h3>';
                    echo '</div></div>';
                }
            endif;
            wp_reset_postdata();
            ?>
        </div>

        <?php if (current_user_can('manage_options') && empty($hot_news_posts)) : ?>
            <div class="admin-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 8px;">
                <p style="margin: 0; color: #856404;">
                    <strong>⚠️ Admin Notice:</strong> Berita Terpanas belum dikonfigurasi.
                    Saat ini menampilkan postingan terbaru secara otomatis.
                </p>
                <p style="margin: 10px 0 0 0;">
                    <a href="<?php echo admin_url('admin.php?page=bawana-hot-news'); ?>"
                       style="background: #d4af37; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block;">
                       🔥 Kelola Berita Terpanas
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=bawana-homepage'); ?>"
                       style="background: #1e3a8a; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block; margin-left: 10px;">
                       ⚙️ Pengaturan Beranda
                    </a>
                </p>
            </div>
        <?php endif; ?>
    </section>
    <?php endif; ?>

    <!-- BAGIAN VIDEO SECTION (NEW) -->
    <?php
    // Only render if video section is enabled AND function exists
    if (get_option('bawana_show_video_section', 0) && function_exists('bawana_render_video_section')) {
        bawana_render_video_section();
    }
    ?>

    <!-- BAGIAN 2: ARTIKEL POPULER (TOP ARTICLES) -->
    <?php if (get_option('bawana_show_popular_articles', 1)) : ?>
    <section class="bawana-section bawana-top-articles">
        <div class="section-header">
            <h2 class="section-title"><?php echo esc_html(get_option('bawana_popular_articles_title', 'Artikel Populer')); ?></h2>
            <?php if (current_user_can('manage_options')) : ?>
                <a href="<?php echo admin_url('admin.php?page=bawana-homepage'); ?>" class="admin-edit-link">⚙️ Pengaturan</a>
            <?php endif; ?>
        </div>
        <div class="bawana-list-3">
             <?php
            // Query untuk artikel populer - fallback ke postingan terbaru
            $top_query = new WP_Query(array(
                'posts_per_page' => 3,
                'ignore_sticky_posts' => 1,
                'post_status' => 'publish',
                'orderby' => 'date',
                'order' => 'DESC',
                'offset' => 0 // Mulai dari postingan pertama
            ));
            if ($top_query->have_posts()) :
                while ($top_query->have_posts()) : $top_query->the_post();
                    // Struktur HTML untuk setiap artikel populer
                    echo '<div class="list-item">';
                    echo '<a class="item-image" href="' . get_the_permalink() . '">';
                    echo bawana_get_post_thumbnail(get_the_ID(), 'medium', 'popular-article-thumb');
                    echo '</a>';
                    echo '<div class="item-content">';
                    $category = get_the_category();
                    if ($category) {
                        echo '<a class="item-category" href="' . get_category_link($category[0]->term_id) . '">' . $category[0]->name . '</a>';
                    }
                    echo '<h3 class="item-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';
                    echo '<a class="read-more" href="' . get_the_permalink() . '">BACA SELENGKAPNYA</a>';
                    echo '</div></div>';
                endwhile;
            else :
                // Fallback jika belum ada postingan
                for ($i = 1; $i <= 3; $i++) {
                    echo '<div class="list-item placeholder">';
                    echo '<div class="item-content">';
                    echo '<span class="item-category">KATEGORI</span>';
                    echo '<h3 class="item-title">Belum ada artikel populer. Silakan tambahkan postingan baru.</h3>';
                    echo '<a class="read-more" href="#">BACA SELENGKAPNYA</a>';
                    echo '</div></div>';
                }
            endif;
            wp_reset_postdata();
            ?>
        </div>
    </section>
    <?php endif; ?>

    <?php
    // BAGIAN 3: POSTINGAN UNGGULAN (FEATURED POSTS) - HANYA TAMPIL JIKA ADA
    // Query untuk Featured Posts - menggunakan meta_key untuk posts yang di-mark sebagai featured
    $featured_query = new WP_Query(array(
        'posts_per_page' => 7,
        'meta_key' => '_bawana_featured_post',
        'meta_value' => '1',
        'post_status' => 'publish',
        'ignore_sticky_posts' => true
    ));

    // HANYA TAMPILKAN SECTION JIKA ADA POSTINGAN UNGGULAN DAN DIAKTIFKAN ADMIN
    if ($featured_query->have_posts() && get_option('bawana_show_featured_posts', 1)) :
        $post_count = 0;
    ?>
        <section class="bawana-section bawana-featured-posts">
            <div class="section-header">
                <h2 class="section-title"><?php echo esc_html(get_option('bawana_featured_posts_title', 'Postingan Unggulan')); ?></h2>
                <?php if (current_user_can('manage_options')) : ?>
                    <a href="<?php echo admin_url('edit.php'); ?>" class="admin-edit-link">✏️ Kelola Postingan</a>
                <?php endif; ?>
            </div>
            <div class="bawana-featured-grid">
                <?php while ($featured_query->have_posts()) : $featured_query->the_post();
                    $post_count++;
                    $item_class = ($post_count <= 2) ? 'featured-large' : 'featured-small';
                ?>
                    <div class="featured-item <?php echo $item_class; ?>">
                        <div class="item-image">
                            <a href="<?php the_permalink(); ?>">
                                <?php echo bawana_get_post_thumbnail(get_the_ID(), ($post_count <= 2) ? 'large' : 'medium', 'featured-post-thumb'); ?>
                            </a>
                        </div>
                        <div class="item-content">
                            <div class="item-category">
                                <?php
                                $categories = get_the_category();
                                if (!empty($categories)) {
                                    echo '<a href="' . esc_url(get_category_link($categories[0]->term_id)) . '">' . esc_html($categories[0]->name) . '</a>';
                                }
                                ?>
                            </div>
                            <h3 class="item-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>
                            <?php if ($post_count <= 2) : ?>
                                <div class="item-excerpt">
                                    <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                </div>
                            <?php endif; ?>
                            <a href="<?php the_permalink(); ?>" class="read-more">Baca Selengkapnya</a>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </section>
    <?php
    endif;
    wp_reset_postdata();
    ?>

    <!-- BAGIAN 4: BERITA TERKINI (LATEST STORIES) - PERBAIKAN KONFLIK -->
    <?php if (get_option('bawana_show_latest_stories', 1)) : ?>
    <section class="bawana-section latest-stories-section">
        <div class="section-header">
            <h2 class="section-title"><?php echo esc_html(get_option('bawana_latest_stories_title', 'Berita Terkini')); ?></h2>
            <?php if (current_user_can('manage_options')) : ?>
                <a href="<?php echo admin_url('admin.php?page=bawana-homepage'); ?>" class="admin-edit-link">⚙️ Pengaturan</a>
            <?php endif; ?>
        </div>
        <div class="latest-stories-grid">
            <?php
            // PERBAIKAN: Gunakan parameter yang konsisten dengan AJAX
            $paged = 1; // Default untuk initial load
            if (isset($_GET['latest_page'])) {
                $paged = intval($_GET['latest_page']);
            } elseif (isset($_GET['page'])) {
                $paged = intval($_GET['page']);
            }
            if ($paged < 1) $paged = 1;
            $posts_per_page = 6;

            $latest_query = new WP_Query(array(
                'posts_per_page' => $posts_per_page,
                'orderby' => 'date',
                'order' => 'DESC',
                'post_status' => 'publish',
                'paged' => $paged,
                'ignore_sticky_posts' => true
            ));

            // DEBUG: Log template query results
            error_log('Template Latest Stories Query - Page: ' . $paged . ', Found posts: ' . $latest_query->found_posts . ', Max pages: ' . $latest_query->max_num_pages);

            if ($latest_query->have_posts()) :
                while ($latest_query->have_posts()) : $latest_query->the_post();
                    $categories = get_the_category();
                    $category_name = !empty($categories) ? $categories[0]->name : 'Nature';

                    echo '<div class="latest-story-item">';
                    echo '<div class="story-image">';
                    echo '<a href="' . get_the_permalink() . '">';
                    echo bawana_get_post_thumbnail(get_the_ID(), 'large', 'latest-story-thumb');
                    echo '</a>';
                    echo '</div>';
                    echo '<div class="story-content">';
                    echo '<div class="story-category">' . esc_html($category_name) . '</div>';
                    echo '<h3 class="story-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';

                    // Add excerpt for side-by-side layout
                    $excerpt = get_the_excerpt();
                    if (empty($excerpt)) {
                        $excerpt = wp_trim_words(get_the_content(), 15, '...');
                    }
                    echo '<div class="story-excerpt">' . esc_html($excerpt) . '</div>';

                    echo '</div>';
                    echo '</div>';
                endwhile;
            else :
                echo '<p style="text-align: center; color: #666; padding: 40px;">Belum ada berita terkini.</p>';
            endif;
            wp_reset_postdata();
            ?>
        </div>

        <!-- PERBAIKAN: AJAX Pagination yang benar -->
        <?php if ($latest_query->max_num_pages > 1) : ?>
        <div class="bawana-pagination" data-max-pages="<?php echo $latest_query->max_num_pages; ?>" data-current-page="<?php echo $paged; ?>">
            <?php
            // Previous button
            if ($paged > 1) {
                echo '<a href="#" class="page-numbers prev ajax-page-link" data-page="' . ($paged - 1) . '">‹</a>';
            }

            // Page numbers
            for ($i = 1; $i <= $latest_query->max_num_pages; $i++) {
                if ($i == $paged) {
                    echo '<span class="page-numbers current">' . $i . '</span>';
                } else {
                    echo '<a href="#" class="page-numbers ajax-page-link" data-page="' . $i . '">' . $i . '</a>';
                }
            }

            // Next button
            if ($paged < $latest_query->max_num_pages) {
                echo '<a href="#" class="page-numbers next ajax-page-link" data-page="' . ($paged + 1) . '">›</a>';
            }
            ?>
        </div>

        <!-- Loading indicator -->
        <div class="latest-stories-loading" style="display: none; text-align: center; padding: 20px;">
            <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #d4af37; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <p style="margin-top: 10px; color: #666;">Memuat berita...</p>
        </div>
        <?php endif; ?>
    </section>
    <?php endif; ?>

    <?php
    } // End of else block for original template structure
    ?>

</div>

<?php 
get_footer(); 
?>
