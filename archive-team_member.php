<?php get_header(); ?>

<div class="td-main-content-wrap td-container-wrap">
    <div class="td-container">
        <div class="td-pb-row">
            <div class="td-pb-span12 td-main-content">
                <div class="td-ss-main-content">
                    <div class="td-page-header">
                        <h1 class="entry-title td-page-title"><PERSON><PERSON> & <PERSON></h1>
                        <p class="team-description">Tim profesional yang berdedikasi untuk memberikan informasi terpercaya kepada masyarakat.</p>
                    </div>
                    
                    <div class="team-grid">
                        <?php
                        $args = array(
                            'post_type' => 'team_member',
                            'posts_per_page' => -1,
                            'meta_key' => 'urutan_tampilan',
                            'orderby' => 'meta_value_num',
                            'order' => 'ASC'
                        );
                        $team_query = new WP_Query($args);
                        
                        if ($team_query->have_posts()) : 
                            while ($team_query->have_posts()) : $team_query->the_post();
                        ?>
                            <div class="team-member-card">
                                <a href="<?php the_permalink(); ?>">
                                    <div class="team-member-image">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium_large'); ?>
                                        <?php else: ?>
                                            <img src="<?php echo get_stylesheet_directory_uri(); ?>/images/placeholder-avatar.svg" alt="<?php the_title(); ?>">
                                        <?php endif; ?>
                                    </div>
                                    <div class="team-member-info">
                                        <h3 class="team-member-name"><?php the_title(); ?></h3>
                                        <p class="team-member-title"><?php the_field('jabatan'); ?></p>
                                        <span class="team-member-link">Lihat Profil →</span>
                                    </div>
                                </a>
                            </div>
                        <?php 
                            endwhile; 
                            wp_reset_postdata(); 
                        else: 
                        ?>
                            <div class="no-team-members">
                                <p>Belum ada anggota tim yang ditambahkan.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
