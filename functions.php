<?php
/**
 * Newspaper Child Theme Functions
 * Kustomisasi untuk Bawana News
 */

// Cache busting untuk homepage - PERBAIKAN MASALAH CACHE
add_action('template_redirect', 'bawana_cache_busting');
function bawana_cache_busting() {
    if (is_page_template('template-beranda.php') || is_front_page()) {
        // Smart cache: Only disable cache for admin when editing
        if (current_user_can('manage_options') && (isset($_GET['bawana_force_refresh']) || isset($_POST['submit']))) {
            // Disable caching untuk admin saat edit
            header('Cache-Control: no-cache, no-store, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');
            delete_transient('bawana_homepage_cache');
            wp_cache_flush();
        } else {
            // For regular users: Enable smart caching (5 minutes untuk SEO)
            header('Cache-Control: public, max-age=300');
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 300) . ' GMT');
        }

        // PERBAIKAN: Force refresh dengan validasi nonce untuk keamanan
        if (isset($_GET['bawana_force_refresh']) && current_user_can('manage_options')) {
            // Validasi nonce untuk keamanan
            if (isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'bawana_force_refresh')) {
                // Clear all relevant options
                delete_transient('bawana_homepage_cache');
                wp_cache_flush();

                // PERBAIKAN: Redirect yang lebih aman
                $redirect_url = remove_query_arg(array('bawana_force_refresh', '_wpnonce'));
                if (wp_safe_redirect($redirect_url)) {
                    exit;
                }
            }
        }
    }
}

/* ----------------------------------------------------------------------------
 * PANEL ADMIN SEO - Pengaturan SEO untuk Google Search
 */
add_action('admin_menu', 'bawana_add_seo_admin_panel');
function bawana_add_seo_admin_panel() {
    add_menu_page(
        'Pengaturan SEO Bawana News',
        'SEO Bawana',
        'manage_options',
        'bawana-seo-settings',
        'bawana_seo_admin_page',
        'dashicons-search',
        25
    );
}

function bawana_seo_admin_page() {
    // Handle form submission
    if (isset($_POST['submit']) && wp_verify_nonce($_POST['bawana_seo_nonce'], 'bawana_seo_settings')) {
        // Update site title dan description
        if (isset($_POST['site_title'])) {
            update_option('blogname', sanitize_text_field($_POST['site_title']));
        }
        if (isset($_POST['site_description'])) {
            update_option('blogdescription', sanitize_text_field($_POST['site_description']));
        }

        // Update custom meta description untuk homepage
        if (isset($_POST['homepage_meta_description'])) {
            update_option('bawana_homepage_meta_description', sanitize_textarea_field($_POST['homepage_meta_description']));
        }

        // Update custom meta keywords
        if (isset($_POST['homepage_meta_keywords'])) {
            update_option('bawana_homepage_meta_keywords', sanitize_text_field($_POST['homepage_meta_keywords']));
        }

        // Update Google Search Console verification
        if (isset($_POST['google_verification'])) {
            update_option('bawana_google_verification', sanitize_text_field($_POST['google_verification']));
        }

        // Update Bing verification
        if (isset($_POST['bing_verification'])) {
            update_option('bawana_bing_verification', sanitize_text_field($_POST['bing_verification']));
        }

        // Update Open Graph settings
        if (isset($_POST['og_default_image'])) {
            update_option('bawana_og_default_image', esc_url_raw($_POST['og_default_image']));
        }

        // Handle favicon upload
        if (isset($_FILES['favicon']) && !empty($_FILES['favicon']['name'])) {
            $uploaded_file = $_FILES['favicon'];

            // Validate file type
            $allowed_types = array('image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/gif', 'image/jpeg');
            if (in_array($uploaded_file['type'], $allowed_types)) {
                $upload_dir = wp_upload_dir();
                $favicon_dir = $upload_dir['basedir'] . '/favicon/';

                // Create favicon directory if it doesn't exist
                if (!file_exists($favicon_dir)) {
                    wp_mkdir_p($favicon_dir);
                }

                $favicon_path = $favicon_dir . 'favicon.ico';

                if (move_uploaded_file($uploaded_file['tmp_name'], $favicon_path)) {
                    $favicon_url = $upload_dir['baseurl'] . '/favicon/favicon.ico';
                    update_option('bawana_custom_favicon', $favicon_url);
                    echo '<div class="notice notice-success"><p>Favicon berhasil diupload!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Gagal mengupload favicon.</p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p>Format file favicon tidak valid. Gunakan .ico, .png, .gif, atau .jpg</p></div>';
            }
        }

        echo '<div class="notice notice-success"><p>Pengaturan SEO berhasil disimpan!</p></div>';
    }

    // Get current values
    $site_title = get_option('blogname');
    $site_description = get_option('blogdescription');
    $homepage_meta_description = get_option('bawana_homepage_meta_description', '');
    $homepage_meta_keywords = get_option('bawana_homepage_meta_keywords', '');
    $google_verification = get_option('bawana_google_verification', '');
    $bing_verification = get_option('bawana_bing_verification', '');
    $og_default_image = get_option('bawana_og_default_image', '');
    ?>

    <div class="wrap">
        <h1>🔍 Pengaturan SEO Bawana News</h1>
        <p>Panel ini mengatur bagaimana website Anda tampil di Google Search dan media sosial.</p>

        <form method="post" action="" enctype="multipart/form-data">
            <?php wp_nonce_field('bawana_seo_settings', 'bawana_seo_nonce'); ?>

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="site_title">Judul Website</label>
                        <p class="description">Tampil di tab browser dan hasil pencarian Google</p>
                    </th>
                    <td>
                        <input type="text" id="site_title" name="site_title" value="<?php echo esc_attr($site_title); ?>" class="regular-text" />
                        <p class="description">Contoh: "Bawana News - Portal Berita Terpercaya"</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="site_description">Tagline Website</label>
                        <p class="description">Deskripsi singkat yang tampil di hasil pencarian</p>
                    </th>
                    <td>
                        <input type="text" id="site_description" name="site_description" value="<?php echo esc_attr($site_description); ?>" class="regular-text" />
                        <p class="description">Contoh: "Mendunia" atau "Portal Berita Terkini Indonesia"</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="homepage_meta_description">Meta Description Homepage</label>
                        <p class="description">Deskripsi yang tampil di Google Search untuk halaman utama</p>
                    </th>
                    <td>
                        <textarea id="homepage_meta_description" name="homepage_meta_description" rows="3" cols="50" class="large-text"><?php echo esc_textarea($homepage_meta_description); ?></textarea>
                        <p class="description">Maksimal 160 karakter. Contoh: "Bawana News menyajikan berita terkini nasional, internasional, olahraga, ekonomi, dan teknologi. Sumber berita terpercaya untuk masyarakat Indonesia."</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="homepage_meta_keywords">Meta Keywords</label>
                        <p class="description">Kata kunci untuk SEO (pisahkan dengan koma)</p>
                    </th>
                    <td>
                        <input type="text" id="homepage_meta_keywords" name="homepage_meta_keywords" value="<?php echo esc_attr($homepage_meta_keywords); ?>" class="large-text" />
                        <p class="description">Contoh: "berita terkini, berita nasional, berita internasional, portal berita, bawana news"</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="google_verification">Google Search Console Verification</label>
                        <p class="description">Kode verifikasi dari Google Search Console</p>
                    </th>
                    <td>
                        <input type="text" id="google_verification" name="google_verification" value="<?php echo esc_attr($google_verification); ?>" class="regular-text" />
                        <p class="description">Masukkan hanya kode content, contoh: "abc123def456ghi789"</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="bing_verification">Bing Webmaster Verification</label>
                        <p class="description">Kode verifikasi dari Bing Webmaster Tools</p>
                    </th>
                    <td>
                        <input type="text" id="bing_verification" name="bing_verification" value="<?php echo esc_attr($bing_verification); ?>" class="regular-text" />
                        <p class="description">Masukkan hanya kode content</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="og_default_image">Default Open Graph Image</label>
                        <p class="description">Gambar default untuk share di Facebook/WhatsApp</p>
                    </th>
                    <td>
                        <input type="url" id="og_default_image" name="og_default_image" value="<?php echo esc_url($og_default_image); ?>" class="large-text" />
                        <p class="description">URL gambar berukuran 1200x630 pixel untuk hasil terbaik</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="favicon">Custom Favicon</label>
                        <p class="description">Upload favicon untuk website (akan muncul di Google search results)</p>
                    </th>
                    <td>
                        <?php $current_favicon = get_option('bawana_custom_favicon'); ?>
                        <?php if ($current_favicon) : ?>
                            <div style="margin-bottom: 10px;">
                                <strong>Favicon saat ini:</strong><br>
                                <img src="<?php echo esc_url($current_favicon); ?>" style="width: 32px; height: 32px; margin: 5px 0;" alt="Current Favicon">
                                <br><small><?php echo esc_url($current_favicon); ?></small>
                            </div>
                        <?php endif; ?>
                        <input type="file" id="favicon" name="favicon" accept=".ico,.png,.gif,.jpg,.jpeg" />
                        <p class="description">Format yang didukung: .ico, .png, .gif, .jpg (ukuran ideal: 32x32px atau 16x16px)</p>
                        <p class="description"><strong>Penting:</strong> Setelah upload, tunggu 24-48 jam untuk muncul di Google search results</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Simpan Pengaturan SEO'); ?>
        </form>

        <hr>

        <h2>📊 Preview Google Search</h2>
        <div style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9; max-width: 600px;">
            <div style="color: #1a0dab; font-size: 18px; text-decoration: underline; margin-bottom: 5px;">
                <?php echo esc_html($site_title); ?>
            </div>
            <div style="color: #006621; font-size: 14px; margin-bottom: 5px;">
                <?php echo esc_url(home_url()); ?>
            </div>
            <div style="color: #545454; font-size: 13px; line-height: 1.4;">
                <?php
                $preview_description = !empty($homepage_meta_description) ? $homepage_meta_description : $site_description;
                echo esc_html($preview_description);
                ?>
            </div>
        </div>

        <h2>🔧 Tools SEO</h2>
        <p>
            <a href="https://search.google.com/search-console" target="_blank" class="button">Google Search Console</a>
            <a href="https://www.bing.com/webmasters" target="_blank" class="button">Bing Webmaster Tools</a>
            <a href="https://developers.facebook.com/tools/debug/" target="_blank" class="button">Facebook Debugger</a>
        </p>
    </div>
    <?php
}

// Enhanced SEO meta tags untuk Google search - MENGGUNAKAN PANEL ADMIN
add_action('wp_head', 'bawana_enhanced_seo_meta_from_admin', 1);
function bawana_enhanced_seo_meta_from_admin() {
    if (is_front_page() || is_page_template('template-beranda.php')) {
        $site_name = get_bloginfo('name');
        $site_description = get_bloginfo('description');
        $site_url = home_url();

        // Ambil dari pengaturan admin panel
        $meta_description = get_option('bawana_homepage_meta_description');
        if (empty($meta_description)) {
            $meta_description = "Bawana News menyajikan berita terkini nasional, internasional, olahraga, ekonomi, dan teknologi. Sumber berita terpercaya untuk masyarakat Indonesia.";
        }

        $meta_keywords = get_option('bawana_homepage_meta_keywords');
        if (empty($meta_keywords)) {
            $meta_keywords = "berita terkini, berita nasional, berita internasional, olahraga, ekonomi, teknologi, bawana news";
        }

        echo '<meta name="description" content="' . esc_attr($meta_description) . '" />' . "\n";
        echo '<meta name="keywords" content="' . esc_attr($meta_keywords) . '" />' . "\n";
        echo '<meta name="author" content="' . esc_attr($site_name) . '" />' . "\n";
        echo '<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />' . "\n";

        // Google Search Console verification
        $google_verification = get_option('bawana_google_verification');
        if (!empty($google_verification)) {
            echo '<meta name="google-site-verification" content="' . esc_attr($google_verification) . '" />' . "\n";
        }

        // Bing verification
        $bing_verification = get_option('bawana_bing_verification');
        if (!empty($bing_verification)) {
            echo '<meta name="msvalidate.01" content="' . esc_attr($bing_verification) . '" />' . "\n";
        }

        // Custom Favicon - PERBAIKAN UNTUK GOOGLE SEARCH RESULTS
        $custom_favicon = get_option('bawana_custom_favicon');
        if (!empty($custom_favicon)) {
            echo '<link rel="icon" type="image/x-icon" href="' . esc_url($custom_favicon) . '" />' . "\n";
            echo '<link rel="shortcut icon" type="image/x-icon" href="' . esc_url($custom_favicon) . '" />' . "\n";
            echo '<link rel="apple-touch-icon" href="' . esc_url($custom_favicon) . '" />' . "\n";
            echo '<link rel="apple-touch-icon-precomposed" href="' . esc_url($custom_favicon) . '" />' . "\n";
        }

        // Canonical URL
        echo '<link rel="canonical" href="' . esc_url($site_url) . '" />' . "\n";

        // Open Graph tags untuk social media
        echo '<meta property="og:locale" content="id_ID" />' . "\n";
        echo '<meta property="og:type" content="website" />' . "\n";
        echo '<meta property="og:title" content="' . esc_attr($site_name . ' - ' . $site_description) . '" />' . "\n";
        echo '<meta property="og:description" content="' . esc_attr($meta_description) . '" />' . "\n";
        echo '<meta property="og:url" content="' . esc_url($site_url) . '" />' . "\n";

        // Default OG image dari pengaturan admin
        $image_url = get_option('bawana_og_default_image');
        if (empty($image_url)) {
            $image_url = get_stylesheet_directory_uri() . '/images/bawana-news-og.jpg';
            if (!file_exists(get_stylesheet_directory() . '/images/bawana-news-og.jpg')) {
                $image_url = get_stylesheet_directory_uri() . '/images/logo.png';
            }
        }

        echo '<meta property="og:image" content="' . esc_url($image_url) . '" />' . "\n";
        echo '<meta property="og:image:width" content="1200" />' . "\n";
        echo '<meta property="og:image:height" content="630" />' . "\n";
        echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '" />' . "\n";

        // Twitter Card tags
        echo '<meta name="twitter:card" content="summary_large_image" />' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr($site_name . ' - ' . $site_description) . '" />' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr($meta_description) . '" />' . "\n";
        echo '<meta name="twitter:image" content="' . esc_url($image_url) . '" />' . "\n";

        // Additional structured data
        echo '<meta name="theme-color" content="#1a1a1a" />' . "\n";
        echo '<meta name="msapplication-TileColor" content="#1a1a1a" />' . "\n";
    }
}

// Enqueue parent theme styles and scripts with proper priority
add_action( 'wp_enqueue_scripts', 'bawana_news_child_enqueue_styles', 99 );
function bawana_news_child_enqueue_styles() {
    // Muat stylesheet dari Tema Parent (Newspaper)
    wp_enqueue_style( 'td-theme-parent', get_template_directory_uri() . '/style.css', false, '12.7.1', 'all' );

    // Muat stylesheet dari Child Theme SETELAH Parent
    wp_enqueue_style( 'bawana-news-child-style', get_stylesheet_uri(), array( 'td-theme-parent' ), '1.0.1', 'all' );
}

// REMOVED - Duplicate favicon function removed to prevent conflicts
// Using bawana_enhanced_favicon_seo instead

// Enhanced Open Graph meta tags for better social sharing
add_action('wp_head', 'bn_enhanced_open_graph_tags');
function bn_enhanced_open_graph_tags() {
    if (is_single() || is_page()) {
        global $post;

        // Get post title
        $title = get_the_title();

        // Get post excerpt or content
        $description = '';
        if (has_excerpt()) {
            $description = get_the_excerpt();
        } else {
            $description = wp_trim_words(strip_tags(get_the_content()), 30, '...');
        }

        // Get post URL
        $url = get_permalink();

        // Get image for sharing
        $image_url = '';

        // Priority 1: Featured image
        if (has_post_thumbnail()) {
            $image_url = get_the_post_thumbnail_url($post->ID, 'large');
        } else {
            // Priority 2: First image from post content
            $content = get_the_content();
            preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches);
            if (!empty($matches[1])) {
                $image_url = $matches[1];
            } else {
                // Priority 3: Site logo as fallback
                $custom_logo_id = get_theme_mod('custom_logo');
                if ($custom_logo_id) {
                    $image_url = wp_get_attachment_image_url($custom_logo_id, 'large');
                } else {
                    // Final fallback: Bawana logo
                    $image_url = get_stylesheet_directory_uri() . '/images/bawana-logo.png';
                }
            }
        }

        // Output Open Graph tags
        echo '<meta property="og:title" content="' . esc_attr($title) . '" />' . "\n";
        echo '<meta property="og:description" content="' . esc_attr($description) . '" />' . "\n";
        echo '<meta property="og:url" content="' . esc_url($url) . '" />' . "\n";
        echo '<meta property="og:type" content="article" />' . "\n";
        echo '<meta property="og:image" content="' . esc_url($image_url) . '" />' . "\n";
        echo '<meta property="og:image:width" content="1200" />' . "\n";
        echo '<meta property="og:image:height" content="630" />' . "\n";
        echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '" />' . "\n";

        // Twitter Card tags
        echo '<meta name="twitter:card" content="summary_large_image" />' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr($title) . '" />' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr($description) . '" />' . "\n";
        echo '<meta name="twitter:image" content="' . esc_url($image_url) . '" />' . "\n";
    }
}

// Fallback menu for header if no menu is set
function bawana_fallback_menu() {
    echo '<ul class="bawana-main-menu">';
    echo '<li><a href="' . home_url() . '">BERANDA</a></li>';

    // Get categories for menu
    $categories = get_categories(array(
        'orderby' => 'name',
        'order' => 'ASC',
        'hide_empty' => true,
        'number' => 6
    ));

    foreach ($categories as $category) {
        echo '<li><a href="' . get_category_link($category->term_id) . '">' . strtoupper($category->name) . '</a></li>';
    }

    echo '</ul>';
}

// REMOVED - Duplicate menu registration removed to prevent conflicts
// Using bawana_news_setup_theme instead

// Create "Tentang Kami" page automatically and add to footer
add_action('init', 'bawana_create_tentang_kami_page');
function bawana_create_tentang_kami_page() {
    // Check if page already exists
    $tentang_kami_page = get_page_by_path('tentang-kami');

    if (!$tentang_kami_page) {
        // Create the page
        $page_data = array(
            'post_title' => 'Tentang Kami',
            'post_content' => 'Halaman ini menggunakan template khusus untuk menampilkan informasi tentang Bawana News.',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_slug' => 'tentang-kami',
            'page_template' => 'page-tentang-kami.php'
        );

        $page_id = wp_insert_post($page_data);

        if ($page_id) {
            // Set the page template
            update_post_meta($page_id, '_wp_page_template', 'page-tentang-kami.php');
        }
    }
}

// Add "Tentang Kami" page to footer menu automatically
add_filter('wp_nav_menu_items', 'bawana_add_tentang_kami_to_footer', 10, 2);
function bawana_add_tentang_kami_to_footer($items, $args) {
    // Only add to footer menu
    if ($args->theme_location == 'footer') {
        $tentang_kami_page = get_page_by_path('tentang-kami');
        if ($tentang_kami_page) {
            $tentang_kami_url = get_permalink($tentang_kami_page->ID);
            $items .= '<li><a href="' . $tentang_kami_url . '">Tentang Kami</a></li>';
        }
    }
    return $items;
}

// REMOVED - Old favicon function replaced with enhanced Google-optimized version

// Register menu locations for Bawana News
add_action( 'after_setup_theme', 'bawana_news_setup_theme' );
function bawana_news_setup_theme() {
    // Add theme support for custom logo
    add_theme_support( 'custom-logo', array(
        'height'      => 100,
        'width'       => 300,
        'flex-height' => true,
        'flex-width'  => true,
    ) );

    // Register navigation menus
    register_nav_menus( array(
        'header-menu' => __( 'Header Menu', 'bawana-news' ),
        'footer-menu' => __( 'Footer Menu', 'bawana-news' ),
    ) );
}

// Force create static homepage - run immediately
add_action('init', 'bawana_force_create_homepage', 5);
function bawana_force_create_homepage() {
    // Always check and create if needed
    $homepage = get_page_by_title('Beranda');

    if (!$homepage) {
        // Create homepage
        $homepage_id = wp_insert_post(array(
            'post_title'    => 'Beranda',
            'post_content'  => '<!-- Halaman beranda kustom Bawana News -->',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_author'   => 1,
        ));

        if ($homepage_id && !is_wp_error($homepage_id)) {
            // Set custom template
            update_post_meta($homepage_id, '_wp_page_template', 'template-beranda.php');

            // Set as front page
            update_option('show_on_front', 'page');
            update_option('page_on_front', $homepage_id);

            // Set blog page (optional)
            $blog_page = get_page_by_title('Blog');
            if (!$blog_page) {
                $blog_page_id = wp_insert_post(array(
                    'post_title'    => 'Blog',
                    'post_content'  => '',
                    'post_status'   => 'publish',
                    'post_type'     => 'page',
                    'post_author'   => 1,
                ));
                if ($blog_page_id && !is_wp_error($blog_page_id)) {
                    update_option('page_for_posts', $blog_page_id);
                }
            }
        }
    } else {
        // Halaman sudah ada, pastikan template dan pengaturan benar
        $homepage_id = $homepage->ID;

        // Update template jika belum diset
        $current_template = get_post_meta($homepage_id, '_wp_page_template', true);
        if ($current_template !== 'template-beranda.php') {
            update_post_meta($homepage_id, '_wp_page_template', 'template-beranda.php');
        }

        // Pastikan diset sebagai front page
        if (get_option('show_on_front') !== 'page' || get_option('page_on_front') != $homepage_id) {
            update_option('show_on_front', 'page');
            update_option('page_on_front', $homepage_id);
        }
    }
}

// Add admin action to force homepage setup
add_action('admin_init', 'bawana_check_homepage_setup');
function bawana_check_homepage_setup() {
    // PERBAIKAN: Check if we need to force setup dengan nonce validation
    if (isset($_GET['force_homepage_setup']) && current_user_can('manage_options')) {
        // Validasi nonce untuk keamanan
        if (isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'force_homepage_setup')) {
            bawana_force_create_homepage();
            wp_safe_redirect(admin_url('admin.php?page=td_theme_panel&setup=complete'));
            exit;
        }
    }

    // Force refresh all settings
    if (isset($_GET['bawana_force_refresh']) && current_user_can('manage_options')) {
        // Delete all setup flags to force recreation
        delete_option('bawana_news_setup_done');
        delete_option('bawana_news_child_setup_done');
        delete_option('bawana_sidebar_setup_done');

        // Force homepage creation
        bawana_force_create_homepage();

        // Force child theme setup
        bawana_news_child_setup();

        // Force create dummy posts if needed
        $post_count = wp_count_posts()->publish;
        if ($post_count < 5) {
            bawana_news_child_setup(); // This will create dummy posts
        }

        // Force menu creation
        bawana_create_default_menu();

        // Clear any caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        wp_safe_redirect(admin_url('?refresh=complete'));
        exit;
    }
}

// Create default menu automatically
add_action('after_setup_theme', 'bawana_create_default_menu');
function bawana_create_default_menu() {
    // Check if header menu already exists
    $menu_name = 'Header Menu Bawana';
    $menu_exists = wp_get_nav_menu_object($menu_name);

    if (!$menu_exists) {
        // Create menu
        $menu_id = wp_create_nav_menu($menu_name);

        if ($menu_id && !is_wp_error($menu_id)) {
            // Add menu items
            $menu_items = array(
                array('title' => 'NASIONAL', 'url' => home_url('/category/nasional/')),
                array('title' => 'INTERNASIONAL', 'url' => home_url('/category/internasional/')),
                array('title' => 'OLAHRAGA', 'url' => home_url('/category/olahraga/')),
                array('title' => 'EKONOMI', 'url' => home_url('/category/ekonomi/')),
                array('title' => 'TEKNOLOGI', 'url' => home_url('/category/teknologi/')),
            );

            foreach ($menu_items as $item) {
                wp_update_nav_menu_item($menu_id, 0, array(
                    'menu-item-title' => $item['title'],
                    'menu-item-url' => $item['url'],
                    'menu-item-status' => 'publish',
                    'menu-item-type' => 'custom'
                ));
            }

            // Assign menu to location
            $locations = get_theme_mod('nav_menu_locations');
            $locations['header-menu'] = $menu_id;
            set_theme_mod('nav_menu_locations', $locations);
        }
    }
}

// Create footer pages automatically
function bawana_create_footer_pages() {
    // Check if pages already exist
    $pages_to_create = array(
        'advertisement' => array(
            'title' => 'Advertisement',
            'template' => 'page-advertisement.php'
        ),
        'contact' => array(
            'title' => 'Contact',
            'template' => 'page-contact.php'
        ),
        'redaksi' => array(
            'title' => 'Redaksi',
            'template' => 'page-redaksi.php'
        ),
        'pedoman-media-siber' => array(
            'title' => 'Pedoman Media Siber',
            'template' => 'page-pedoman-media-siber.php'
        ),
        'kode-etik-jurnalistik' => array(
            'title' => 'Kode Etik Jurnalistik',
            'template' => 'page-kode-etik.php'
        )
    );

    $created_page_ids = array();

    foreach ($pages_to_create as $slug => $page_data) {
        // Check if page already exists
        $existing_page = get_page_by_path($slug);

        if (!$existing_page) {
            // Create the page with proper content
            $page_id = wp_insert_post(array(
                'post_title' => $page_data['title'],
                'post_name' => $slug,
                'post_content' => 'Konten untuk halaman ' . $page_data['title'] . ' akan ditambahkan di sini.',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1
            ));

            if ($page_id && !is_wp_error($page_id)) {
                // Set the page template
                update_post_meta($page_id, '_wp_page_template', $page_data['template']);
                $created_page_ids[$slug] = $page_id;
            }
        } else {
            // Update existing page to use correct template
            update_post_meta($existing_page->ID, '_wp_page_template', $page_data['template']);
            $created_page_ids[$slug] = $existing_page->ID;
        }
    }

    // Fix footer menu with all pages
    bawana_fix_footer_menu($created_page_ids);
}

// Function to recreate pages with proper content and fix footer menu
function bawana_recreate_pages() {
    $pages_to_recreate = array(
        'advertisement' => array(
            'title' => 'Advertisement',
            'template' => 'page-advertisement.php'
        ),
        'contact' => array(
            'title' => 'Contact',
            'template' => 'page-contact.php'
        ),
        'redaksi' => array(
            'title' => 'Redaksi',
            'template' => 'page-redaksi.php'
        ),
        'pedoman-media-siber' => array(
            'title' => 'Pedoman Media Siber',
            'template' => 'page-pedoman-media-siber.php'
        ),
        'kode-etik-jurnalistik' => array(
            'title' => 'Kode Etik Jurnalistik',
            'template' => 'page-kode-etik.php'
        )
    );

    $created_page_ids = array();

    foreach ($pages_to_recreate as $slug => $page_data) {
        // Delete existing page if it exists
        $existing_page = get_page_by_path($slug);
        if ($existing_page) {
            wp_delete_post($existing_page->ID, true);
        }

        // Create new page with proper content
        $page_id = wp_insert_post(array(
            'post_title' => $page_data['title'],
            'post_name' => $slug,
            'post_content' => 'Konten untuk halaman ' . $page_data['title'] . ' akan ditampilkan menggunakan template khusus.',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_author' => 1
        ));

        if ($page_id && !is_wp_error($page_id)) {
            // Set the page template
            update_post_meta($page_id, '_wp_page_template', $page_data['template']);
            $created_page_ids[$slug] = $page_id;
        }
    }

    // Now fix the footer menu
    bawana_fix_footer_menu($created_page_ids);
}

// Function to fix footer menu comprehensively
function bawana_fix_footer_menu($page_ids = null) {
    // Try different possible menu names
    $possible_menu_names = array('Footer Informasi', 'Footer Menu', 'footer-menu');
    $footer_menu = null;

    foreach ($possible_menu_names as $menu_name) {
        $footer_menu = wp_get_nav_menu_object($menu_name);
        if ($footer_menu) {
            break;
        }
    }

    // If no footer menu exists, create one
    if (!$footer_menu) {
        $footer_menu_id = wp_create_nav_menu('Footer Informasi');
        $footer_menu = wp_get_nav_menu_object($footer_menu_id);

        // Assign to footer menu location
        $locations = get_theme_mod('nav_menu_locations');
        $locations['footer-menu'] = $footer_menu_id;
        set_theme_mod('nav_menu_locations', $locations);
    }

    if (!$footer_menu) {
        return false; // Could not create or find menu
    }

    // Clear existing menu items
    $menu_items = wp_get_nav_menu_items($footer_menu->term_id);
    if ($menu_items) {
        foreach ($menu_items as $menu_item) {
            wp_delete_post($menu_item->ID, true);
        }
    }

    // Get page IDs if not provided
    if (!$page_ids) {
        $page_ids = array();
        $pages_to_add = array('advertisement', 'contact', 'redaksi', 'pedoman-media-siber', 'kode-etik-jurnalistik');

        foreach ($pages_to_add as $slug) {
            $page = get_page_by_path($slug);
            if ($page) {
                $page_ids[$slug] = $page->ID;
            }
        }
    }

    // Add all pages to footer menu
    foreach ($page_ids as $slug => $page_id) {
        $page_title = get_the_title($page_id);
        wp_update_nav_menu_item($footer_menu->term_id, 0, array(
            'menu-item-title' => $page_title,
            'menu-item-object' => 'page',
            'menu-item-object-id' => $page_id,
            'menu-item-type' => 'post_type',
            'menu-item-status' => 'publish'
        ));
    }

    return true;
}

/**
 * Mengganti "Blog" di breadcrumbs dengan "Berita Terkini"
 * menggunakan metode output buffering yang lebih andal.
 */
add_filter('td_breadcrump_get_path', 'bawana_change_breadcrumb_path');
function bawana_change_breadcrumb_path($path) {
    if ( is_home() ) {
        // str_replace() lebih andal untuk mengganti bagian dari string
        $path = str_replace('> Blog', '> Berita Terkini', $path);
    }
    return $path;
}

// Run on theme activation
add_action('after_switch_theme', 'bawana_create_footer_pages');

// Add admin menu for manual page creation
function bawana_admin_menu() {
    add_theme_page(
        'Bawana Footer Pages',
        'Footer Pages',
        'manage_options',
        'bawana-footer-pages',
        'bawana_footer_pages_admin'
    );
}
add_action('admin_menu', 'bawana_admin_menu');

// Admin page for footer pages management
function bawana_footer_pages_admin() {
    if (isset($_POST['create_pages'])) {
        bawana_create_footer_pages();
        echo '<div class="notice notice-success"><p>Footer pages created successfully!</p></div>';
    }

    if (isset($_POST['recreate_pages'])) {
        bawana_recreate_pages();
        echo '<div class="notice notice-success"><p>Footer pages recreated successfully! Empty content issue should be fixed.</p></div>';
    }

    if (isset($_POST['fix_footer_menu'])) {
        $result = bawana_fix_footer_menu();
        if ($result) {
            echo '<div class="notice notice-success"><p>Footer menu fixed successfully! All pages should now appear in footer.</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>Failed to fix footer menu. Please check if pages exist.</p></div>';
        }
    }

    ?>
    <div class="wrap">
        <h1>Bawana Footer Pages Management</h1>
        <p>Manage footer pages for Bawana News website.</p>

        <div class="card">
            <h2>Create Footer Pages</h2>
            <p>Click the button below to create all footer pages:</p>
            <ul>
                <li><strong>Advertisement</strong> - Form iklan dengan nomor kontak +62 858-9293-9055</li>
                <li><strong>Contact</strong> - Halaman kontak lengkap dengan form dan informasi redaksi</li>
                <li><strong>Redaksi</strong> - Susunan redaksi Bawana News</li>
                <li><strong>Pedoman Media Siber</strong> - Pedoman pemberitaan media siber</li>
                <li><strong>Kode Etik Jurnalistik</strong> - Kode etik jurnalistik lengkap</li>
            </ul>

            <form method="post">
                <input type="submit" name="create_pages" class="button button-primary" value="Create Footer Pages">
            </form>
        </div>

        <div class="card">
            <h2>Fix Empty Content Issue</h2>
            <p>If pages appear empty or show wrong content, use this button to recreate them with proper templates:</p>
            <form method="post">
                <input type="submit" name="recreate_pages" class="button button-secondary" value="Recreate All Pages (Fix Empty Content)" onclick="return confirm('This will delete and recreate all footer pages. Continue?')">
            </form>
        </div>

        <div class="card">
            <h2>Fix Footer Menu</h2>
            <p>If some footer pages are not appearing in the footer menu, use this button to fix the menu:</p>
            <form method="post">
                <input type="submit" name="fix_footer_menu" class="button button-secondary" value="Fix Footer Menu (Show All Pages)">
            </form>
        </div>

        <div class="card">
            <h2>Existing Pages Status</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Page</th>
                        <th>Status</th>
                        <th>URL</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $pages_to_check = array(
                        'advertisement' => 'Advertisement',
                        'contact' => 'Contact',
                        'redaksi' => 'Redaksi',
                        'pedoman-media-siber' => 'Pedoman Media Siber',
                        'kode-etik-jurnalistik' => 'Kode Etik Jurnalistik'
                    );

                    foreach ($pages_to_check as $slug => $title) {
                        $page = get_page_by_path($slug);
                        $status = $page ? '<span style="color: green;">✓ Created</span>' : '<span style="color: red;">✗ Not Found</span>';
                        $url = $page ? '<a href="' . get_permalink($page->ID) . '" target="_blank">View Page</a>' : '-';

                        echo "<tr>";
                        echo "<td><strong>$title</strong></td>";
                        echo "<td>$status</td>";
                        echo "<td>$url</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php
}

/* ----------------------------------------------------------------------------
 * Bawana News Logo Setup
 */
add_action('init', 'bawana_news_logo_setup');
function bawana_news_logo_setup() {
    // Upload logo Bawana News jika belum ada
    $logo_url = get_theme_mod('custom_logo');
    if (!$logo_url) {
        // Set site title dan tagline
        update_option('blogname', 'Bawana News');
        update_option('blogdescription', 'Mendunia');
    }
}

/* ----------------------------------------------------------------------------
 * Custom Header Logo Display
 */
add_filter('get_custom_logo', 'bawana_news_custom_logo');
function bawana_news_custom_logo($html) {
    // Pastikan logo atau teks logo selalu terlihat
    if (empty($html)) {
        // Jika tidak ada logo custom, tampilkan text logo dengan styling
        $site_name = get_bloginfo('name');
        $site_description = get_bloginfo('description');
        
        $html = '<div class="bawana-logo-container" style="visibility: visible !important; opacity: 1 !important; display: flex !important; align-items: center !important;">';
        $html .= '<h1 class="bawana-logo-text" style="visibility: visible !important; opacity: 1 !important;"><a href="' . esc_url(home_url('/')) . '">' . esc_html($site_name) . '</a></h1>';
        $html .= '<p class="bawana-tagline" style="visibility: visible !important; opacity: 1 !important;">' . esc_html($site_description) . '</p>';
        $html .= '</div>';
    }
    return $html;
}

/* ----------------------------------------------------------------------------
 * BAWANA HOMEPAGE CUSTOMIZATION SYSTEM
 */

// Add admin menu for homepage customization
add_action('admin_menu', 'bawana_add_homepage_admin_menu');
function bawana_add_homepage_admin_menu() {
    add_menu_page(
        'Pengaturan Beranda Bawana',
        'Beranda Bawana',
        'manage_options',
        'bawana-homepage',
        'bawana_homepage_admin_page',
        'dashicons-admin-home',
        30
    );

    add_submenu_page(
        'bawana-homepage',
        'Berita Terpanas',
        'Berita Terpanas',
        'manage_options',
        'bawana-hot-news',
        'bawana_hot_news_admin_page'
    );
}

// Admin page for homepage customization
function bawana_homepage_admin_page() {
    if (isset($_POST['save_homepage_settings']) && wp_verify_nonce($_POST['bawana_nonce'], 'bawana_homepage_settings')) {
        // Save homepage settings - properly handle unchecked checkboxes
        update_option('bawana_show_hot_news', isset($_POST['show_hot_news']) ? 1 : 0);
        update_option('bawana_show_featured_posts', isset($_POST['show_featured_posts']) ? 1 : 0);
        update_option('bawana_show_popular_articles', isset($_POST['show_popular_articles']) ? 1 : 0);
        update_option('bawana_show_latest_stories', isset($_POST['show_latest_stories']) ? 1 : 0);
        update_option('bawana_show_sidebar_widgets', isset($_POST['show_sidebar_widgets']) ? 1 : 0);
        update_option('bawana_show_ads_section', isset($_POST['show_ads_section']) ? 1 : 0);

        // Save titles
        update_option('bawana_hot_news_title', sanitize_text_field($_POST['hot_news_title']));
        update_option('bawana_featured_posts_title', sanitize_text_field($_POST['featured_posts_title']));
        update_option('bawana_popular_articles_title', sanitize_text_field($_POST['popular_articles_title']));
        update_option('bawana_latest_stories_title', sanitize_text_field($_POST['latest_stories_title']));

        // Save widget settings
        update_option('bawana_widget_order', sanitize_text_field($_POST['widget_order']));
        update_option('bawana_ads_content', wp_kses_post($_POST['ads_content']));
        update_option('bawana_ads_title', sanitize_text_field($_POST['ads_title']));

        // Save widget content settings
        update_option('bawana_live_streaming_title', sanitize_text_field($_POST['live_streaming_title']));
        update_option('bawana_live_streaming_url', esc_url_raw($_POST['live_streaming_url']));
        update_option('bawana_live_streaming_description', sanitize_textarea_field($_POST['live_streaming_description']));
        update_option('bawana_talkshow_title', sanitize_text_field($_POST['talkshow_title']));
        update_option('bawana_talkshow_description', sanitize_textarea_field($_POST['talkshow_description']));
        update_option('bawana_popular_posts_title', sanitize_text_field($_POST['popular_posts_title']));
        update_option('bawana_popular_posts_count', absint($_POST['popular_posts_count']));

        // Save section order
        update_option('bawana_section_order', sanitize_text_field($_POST['section_order']));

        // Save section order system toggle
        update_option('bawana_use_section_order', isset($_POST['use_section_order']) ? 1 : 0);

        // Save custom sections
        if (isset($_POST['custom_sections'])) {
            $custom_sections = array();
            foreach ($_POST['custom_sections'] as $section) {
                if (!empty($section['title']) && !empty($section['type'])) {
                    $custom_sections[] = array(
                        'id' => sanitize_title($section['title']),
                        'title' => sanitize_text_field($section['title']),
                        'type' => sanitize_text_field($section['type']),
                        'grid' => sanitize_text_field($section['grid']),
                        'category' => intval($section['category']),
                        'count' => intval($section['count'])
                    );
                }
            }
            update_option('bawana_custom_sections', $custom_sections);
        }

        // Save video embed settings
        update_option('bawana_show_video_section', isset($_POST['show_video_section']) ? 1 : 0);
        update_option('bawana_video_title', sanitize_text_field($_POST['video_title']));
        update_option('bawana_video_url', esc_url_raw($_POST['video_url']));
        update_option('bawana_video_autoplay', isset($_POST['video_autoplay']) ? 1 : 0);
        update_option('bawana_video_description', sanitize_textarea_field($_POST['video_description']));

        // Reset sidebar setup to apply new widget order
        update_option('bawana_sidebar_setup_done', false);
        bawana_news_setup_sidebar();

        echo '<div class="notice notice-success">';
        echo '<p>✅ Pengaturan beranda berhasil disimpan!</p>';
        echo '<p>Lihat hasilnya di: <a href="' . home_url() . '" target="_blank">🏠 Halaman Beranda</a></p>';
        if ($use_section_order) {
            echo '<p><strong>Mode:</strong> Menggunakan sistem section order dinamis dengan video embed yang SEO-friendly.</p>';
        } else {
            echo '<p><strong>Mode:</strong> Menggunakan template default dengan video section tambahan.</p>';
        }
        echo '</div>';
    }

    // Get current settings with proper defaults
    $show_hot_news = get_option('bawana_show_hot_news', 1);
    $show_featured_posts = get_option('bawana_show_featured_posts', 1);
    $show_popular_articles = get_option('bawana_show_popular_articles', 1);
    $show_latest_stories = get_option('bawana_show_latest_stories', 1);
    $show_sidebar_widgets = get_option('bawana_show_sidebar_widgets', 1);
    $show_ads_section = get_option('bawana_show_ads_section', 0);
    $use_section_order = get_option('bawana_use_section_order', 0);

    $hot_news_title = get_option('bawana_hot_news_title', 'Berita Terpanas');
    $featured_posts_title = get_option('bawana_featured_posts_title', 'Postingan Unggulan');
    $popular_articles_title = get_option('bawana_popular_articles_title', 'Artikel Populer');
    $latest_stories_title = get_option('bawana_latest_stories_title', 'Berita Terkini');

    $widget_order = get_option('bawana_widget_order', 'live,popular,talkshow');
    $ads_content = get_option('bawana_ads_content', '');
    $ads_title = get_option('bawana_ads_title', 'Advertisement');

    // Widget content settings
    $live_streaming_title = get_option('bawana_live_streaming_title', 'LIVE SEKARANG');
    $live_streaming_url = get_option('bawana_live_streaming_url', '');
    $live_streaming_description = get_option('bawana_live_streaming_description', 'Video live streaming akan muncul di sini. Keda saat Theme Panel → Video Sidebar');
    $talkshow_title = get_option('bawana_talkshow_title', 'TALKSHOW');
    $talkshow_description = get_option('bawana_talkshow_description', 'Informasi talkshow akan muncul di sini.');
    $popular_posts_title = get_option('bawana_popular_posts_title', 'BERITA POPULER');
    $popular_posts_count = get_option('bawana_popular_posts_count', 5);

    // Section order
    $section_order = get_option('bawana_section_order', 'hot_news,popular_articles,featured_posts,video_section,latest_stories');

    // Video embed settings
    $show_video_section = get_option('bawana_show_video_section', 0);
    $video_title = get_option('bawana_video_title', 'Video Unggulan');
    $video_url = get_option('bawana_video_url', '');
    $video_autoplay = get_option('bawana_video_autoplay', 0);
    $video_description = get_option('bawana_video_description', '');
    ?>
    <div class="wrap">
        <h1>🏠 Pengaturan Beranda Bawana News</h1>
        <p>Atur tampilan dan konten halaman beranda website Anda dengan mudah.</p>

        <form method="post" action="">
            <?php wp_nonce_field('bawana_homepage_settings', 'bawana_nonce'); ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <!-- Section Visibility -->
                <div style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2>📋 Tampilkan Section</h2>
                    <table class="form-table">
                        <tr>
                            <td>
                                <label><input type="checkbox" name="show_hot_news" value="1" <?php checked($show_hot_news, 1); ?>> Berita Terpanas</label><br>
                                <label><input type="checkbox" name="show_featured_posts" value="1" <?php checked($show_featured_posts, 1); ?>> Postingan Unggulan</label><br>
                                <label><input type="checkbox" name="show_popular_articles" value="1" <?php checked($show_popular_articles, 1); ?>> Artikel Populer</label><br>
                                <label><input type="checkbox" name="show_latest_stories" value="1" <?php checked($show_latest_stories, 1); ?>> Berita Terkini</label><br>
                                <label><input type="checkbox" name="show_sidebar_widgets" value="1" <?php checked($show_sidebar_widgets, 1); ?>> Widget Sidebar</label><br>
                                <label><input type="checkbox" name="show_ads_section" value="1" <?php checked($show_ads_section, 1); ?>> Panel Iklan</label>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Section Titles -->
                <div style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2>✏️ Judul Section</h2>
                    <table class="form-table">
                        <tr>
                            <td>
                                <p><label>Berita Terpanas: <input type="text" name="hot_news_title" value="<?php echo esc_attr($hot_news_title); ?>" class="regular-text"></label></p>
                                <p><label>Postingan Unggulan: <input type="text" name="featured_posts_title" value="<?php echo esc_attr($featured_posts_title); ?>" class="regular-text"></label></p>
                                <p><label>Artikel Populer: <input type="text" name="popular_articles_title" value="<?php echo esc_attr($popular_articles_title); ?>" class="regular-text"></label></p>
                                <p><label>Berita Terkini: <input type="text" name="latest_stories_title" value="<?php echo esc_attr($latest_stories_title); ?>" class="regular-text"></label></p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Widget Management -->
            <div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
                <h2>🎛️ Pengaturan Widget Sidebar</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Urutan Widget</th>
                        <td>
                            <select name="widget_order" class="regular-text">
                                <option value="live,popular,talkshow" <?php selected($widget_order, 'live,popular,talkshow'); ?>>Live → Popular → Talkshow</option>
                                <option value="popular,live,talkshow" <?php selected($widget_order, 'popular,live,talkshow'); ?>>Popular → Live → Talkshow</option>
                                <option value="live,talkshow,popular" <?php selected($widget_order, 'live,talkshow,popular'); ?>>Live → Talkshow → Popular</option>
                            </select>
                            <p class="description">Pilih urutan tampilan widget di sidebar</p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Widget Content Management -->
            <div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
                <h2>🎛️ Konten Widget Sidebar</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                    <!-- Live Streaming Widget -->
                    <div style="border: 1px solid #eee; padding: 15px; border-radius: 5px;">
                        <h3>📺 Live Streaming</h3>
                        <table class="form-table">
                            <tr>
                                <td>
                                    <label>Judul:</label><br>
                                    <input type="text" name="live_streaming_title" value="<?php echo esc_attr($live_streaming_title); ?>" class="regular-text">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>URL Video:</label><br>
                                    <input type="url" name="live_streaming_url" value="<?php echo esc_attr($live_streaming_url); ?>" class="regular-text" placeholder="https://youtube.com/watch?v=...">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>Deskripsi:</label><br>
                                    <textarea name="live_streaming_description" rows="3" class="regular-text"><?php echo esc_textarea($live_streaming_description); ?></textarea>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Talkshow Widget -->
                    <div style="border: 1px solid #eee; padding: 15px; border-radius: 5px;">
                        <h3>🎙️ Talkshow</h3>
                        <table class="form-table">
                            <tr>
                                <td>
                                    <label>Judul:</label><br>
                                    <input type="text" name="talkshow_title" value="<?php echo esc_attr($talkshow_title); ?>" class="regular-text">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>Deskripsi:</label><br>
                                    <textarea name="talkshow_description" rows="4" class="regular-text"><?php echo esc_textarea($talkshow_description); ?></textarea>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Popular Posts Widget -->
                    <div style="border: 1px solid #eee; padding: 15px; border-radius: 5px;">
                        <h3>🔥 Berita Populer</h3>
                        <table class="form-table">
                            <tr>
                                <td>
                                    <label>Judul:</label><br>
                                    <input type="text" name="popular_posts_title" value="<?php echo esc_attr($popular_posts_title); ?>" class="regular-text">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>Jumlah Post:</label><br>
                                    <select name="popular_posts_count" class="regular-text">
                                        <option value="3" <?php selected($popular_posts_count, 3); ?>>3 Post</option>
                                        <option value="5" <?php selected($popular_posts_count, 5); ?>>5 Post</option>
                                        <option value="7" <?php selected($popular_posts_count, 7); ?>>7 Post</option>
                                        <option value="10" <?php selected($popular_posts_count, 10); ?>>10 Post</option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Section Order Management -->
            <div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
                <h2>📋 Urutan Section Beranda</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Sistem Section Order</th>
                        <td>
                            <label><input type="checkbox" name="use_section_order" value="1" <?php checked($use_section_order, 1); ?>> Aktifkan sistem urutan section dinamis</label>
                            <p class="description">Jika diaktifkan, akan menggunakan template baru dengan section order yang dapat diatur. Jika tidak, menggunakan template default.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Urutan Section Manual</th>
                        <td>
                            <div id="section-order-builder">
                                <p><strong>Drag & Drop untuk mengatur urutan:</strong></p>
                                <ul id="sortable-sections" style="list-style: none; padding: 0;">
                                    <?php
                                    $current_order = explode(',', $section_order);
                                    $available_sections = array(
                                        'hot_news' => '🔥 Berita Terpanas',
                                        'popular_articles' => '📈 Artikel Populer',
                                        'featured_posts' => '⭐ Featured Posts',
                                        'video_section' => '🎬 Video Section',
                                        'latest_stories' => '📰 Berita Terkini'
                                    );

                                    // Add custom sections
                                    $custom_sections = get_option('bawana_custom_sections', array());
                                    foreach ($custom_sections as $custom_section) {
                                        $available_sections[$custom_section['id']] = '🎯 ' . $custom_section['title'];
                                    }

                                    foreach ($current_order as $section) {
                                        $section = trim($section);
                                        if (isset($available_sections[$section])) {
                                            echo '<li data-section="' . $section . '" style="background: #f9f9f9; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px; cursor: move;">';
                                            echo '<span style="margin-right: 10px;">⋮⋮</span>' . $available_sections[$section];
                                            echo '<button type="button" onclick="toggleSection(this)" style="float: right; background: #dc3545; color: white; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer;">Hapus</button>';
                                            echo '</li>';
                                            unset($available_sections[$section]);
                                        }
                                    }

                                    // Add remaining sections
                                    foreach ($available_sections as $key => $label) {
                                        echo '<li data-section="' . $key . '" style="background: #f0f0f0; padding: 10px; margin: 5px 0; border: 1px solid #ccc; border-radius: 5px; cursor: move; opacity: 0.6;">';
                                        echo '<span style="margin-right: 10px;">⋮⋮</span>' . $label . ' <em>(Nonaktif)</em>';
                                        echo '<button type="button" onclick="toggleSection(this)" style="float: right; background: #28a745; color: white; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer;">Aktifkan</button>';
                                        echo '</li>';
                                    }
                                    ?>
                                </ul>
                                <input type="hidden" name="section_order" id="section_order_input" value="<?php echo esc_attr($section_order); ?>">
                                <p class="description">Drag untuk mengubah urutan, klik tombol untuk mengaktifkan/menonaktifkan section</p>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Video Embed Section -->
            <div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
                <h2>🎬 Section Video Unggulan</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Tampilkan Section Video</th>
                        <td>
                            <label><input type="checkbox" name="show_video_section" value="1" <?php checked($show_video_section, 1); ?>> Aktifkan section video di beranda</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Judul Section</th>
                        <td><input type="text" name="video_title" value="<?php echo esc_attr($video_title); ?>" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row">URL Video</th>
                        <td>
                            <input type="url" name="video_url" value="<?php echo esc_attr($video_url); ?>" class="regular-text" placeholder="https://youtube.com/watch?v=... atau https://facebook.com/...">
                            <p class="description">Support YouTube dan Facebook video. Contoh: https://youtube.com/watch?v=dQw4w9WgXcQ</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Autoplay Video</th>
                        <td>
                            <label><input type="checkbox" name="video_autoplay" value="1" <?php checked($video_autoplay, 1); ?>> Aktifkan autoplay (akan dimuat setelah konten lain untuk SEO)</label>
                            <p class="description">⚠️ Autoplay akan menunggu konten lain selesai dimuat untuk performa yang lebih baik</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Deskripsi Video</th>
                        <td>
                            <textarea name="video_description" rows="3" cols="80" placeholder="Deskripsi singkat tentang video..."><?php echo esc_textarea($video_description); ?></textarea>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Ads Panel -->
            <div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
                <h2>📢 Panel Iklan</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Judul Panel</th>
                        <td><input type="text" name="ads_title" value="<?php echo esc_attr($ads_title); ?>" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row">Konten Iklan</th>
                        <td>
                            <textarea name="ads_content" rows="6" cols="80" placeholder="Masukkan kode HTML iklan atau konten promosi..."><?php echo esc_textarea($ads_content); ?></textarea>
                            <p class="description">Bisa berupa kode HTML, gambar, atau teks promosi</p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Custom Section Builder -->
            <div style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px;">
                <h2>➕ Tambah Section Baru</h2>
                <div id="custom-sections-container">
                    <?php
                    $custom_sections = get_option('bawana_custom_sections', array());
                    foreach ($custom_sections as $index => $section) {
                        echo '<div class="custom-section-item" style="border: 1px solid #eee; padding: 15px; margin: 10px 0; border-radius: 5px;">';
                        echo '<div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr auto; gap: 10px; align-items: center;">';
                        echo '<input type="text" name="custom_sections[' . $index . '][title]" placeholder="Judul Section" value="' . esc_attr($section['title']) . '" style="padding: 5px;">';
                        echo '<select name="custom_sections[' . $index . '][type]" style="padding: 5px;">';
                        echo '<option value="latest" ' . selected($section['type'], 'latest', false) . '>Postingan Terbaru</option>';
                        echo '<option value="category" ' . selected($section['type'], 'category', false) . '>Berdasarkan Kategori</option>';
                        echo '<option value="popular" ' . selected($section['type'], 'popular', false) . '>Postingan Populer</option>';
                        echo '</select>';
                        echo '<select name="custom_sections[' . $index . '][grid]" style="padding: 5px;">';
                        echo '<option value="grid-3" ' . selected($section['grid'], 'grid-3', false) . '>Grid 3 Kolom</option>';
                        echo '<option value="grid-4" ' . selected($section['grid'], 'grid-4', false) . '>Grid 4 Kolom</option>';
                        echo '<option value="list" ' . selected($section['grid'], 'list', false) . '>List Vertikal</option>';
                        echo '</select>';
                        echo '<input type="number" name="custom_sections[' . $index . '][count]" placeholder="Jumlah" value="' . esc_attr($section['count']) . '" min="1" max="20" style="padding: 5px;">';
                        echo '<button type="button" onclick="removeCustomSection(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Hapus</button>';
                        echo '</div>';
                        if ($section['type'] === 'category') {
                            echo '<div style="margin-top: 10px;">';
                            echo '<select name="custom_sections[' . $index . '][category]" style="width: 100%; padding: 5px;">';
                            echo '<option value="">Pilih Kategori</option>';
                            $categories = get_categories();
                            foreach ($categories as $cat) {
                                echo '<option value="' . $cat->term_id . '" ' . selected($section['category'], $cat->term_id, false) . '>' . esc_html($cat->name) . '</option>';
                            }
                            echo '</select>';
                            echo '</div>';
                        }
                        echo '</div>';
                    }
                    ?>
                </div>
                <button type="button" onclick="addCustomSection()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">➕ Tambah Section</button>
            </div>

            <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
            <script>
            jQuery(document).ready(function($) {
                // Make sections sortable
                $("#sortable-sections").sortable({
                    update: function(event, ui) {
                        updateSectionOrder();
                    }
                });

                // Update hidden input when order changes
                function updateSectionOrder() {
                    var order = [];
                    $("#sortable-sections li").each(function() {
                        var section = $(this).data('section');
                        var isActive = !$(this).hasClass('inactive');
                        if (isActive) {
                            order.push(section);
                        }
                    });
                    $("#section_order_input").val(order.join(','));
                }

                // Toggle section active/inactive
                window.toggleSection = function(button) {
                    var li = $(button).closest('li');
                    var isActive = !li.hasClass('inactive');

                    if (isActive) {
                        // Deactivate
                        li.addClass('inactive');
                        li.css({'background': '#f0f0f0', 'opacity': '0.6'});
                        li.find('em').remove();
                        li.find('span').after(' <em>(Nonaktif)</em>');
                        $(button).text('Aktifkan').css('background', '#28a745');
                    } else {
                        // Activate
                        li.removeClass('inactive');
                        li.css({'background': '#f9f9f9', 'opacity': '1'});
                        li.find('em').remove();
                        $(button).text('Hapus').css('background', '#dc3545');
                    }

                    updateSectionOrder();
                };

                // Initialize
                updateSectionOrder();
            });

            // Custom section management
            let customSectionIndex = <?php echo count(get_option('bawana_custom_sections', array())); ?>;

            function addCustomSection() {
                const container = document.getElementById('custom-sections-container');
                const newSection = document.createElement('div');
                newSection.className = 'custom-section-item';
                newSection.style.cssText = 'border: 1px solid #eee; padding: 15px; margin: 10px 0; border-radius: 5px;';

                newSection.innerHTML = `
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr auto; gap: 10px; align-items: center;">
                        <input type="text" name="custom_sections[${customSectionIndex}][title]" placeholder="Judul Section" style="padding: 5px;">
                        <select name="custom_sections[${customSectionIndex}][type]" style="padding: 5px;" onchange="toggleCategorySelect(this, ${customSectionIndex})">
                            <option value="latest">Postingan Terbaru</option>
                            <option value="category">Berdasarkan Kategori</option>
                            <option value="popular">Postingan Populer</option>
                        </select>
                        <select name="custom_sections[${customSectionIndex}][grid]" style="padding: 5px;">
                            <option value="grid-3">Grid 3 Kolom</option>
                            <option value="grid-4">Grid 4 Kolom</option>
                            <option value="list">List Vertikal</option>
                        </select>
                        <input type="number" name="custom_sections[${customSectionIndex}][count]" placeholder="Jumlah" value="6" min="1" max="20" style="padding: 5px;">
                        <button type="button" onclick="removeCustomSection(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Hapus</button>
                    </div>
                `;

                container.appendChild(newSection);
                customSectionIndex++;
            }

            function removeCustomSection(button) {
                button.closest('.custom-section-item').remove();
            }

            function toggleCategorySelect(select, index) {
                const container = select.closest('.custom-section-item');
                let categoryDiv = container.querySelector('.category-select');

                if (select.value === 'category') {
                    if (!categoryDiv) {
                        categoryDiv = document.createElement('div');
                        categoryDiv.className = 'category-select';
                        categoryDiv.style.marginTop = '10px';
                        categoryDiv.innerHTML = `
                            <select name="custom_sections[${index}][category]" style="width: 100%; padding: 5px;">
                                <option value="">Pilih Kategori</option>
                                <?php
                                $categories = get_categories();
                                foreach ($categories as $cat) {
                                    echo '<option value="' . $cat->term_id . '">' . esc_html($cat->name) . '</option>';
                                }
                                ?>
                            </select>
                        `;
                        container.appendChild(categoryDiv);
                    }
                } else {
                    if (categoryDiv) {
                        categoryDiv.remove();
                    }
                }
            }
            </script>

            <?php submit_button('Simpan Pengaturan', 'primary', 'save_homepage_settings'); ?>
        </form>

        <hr>
        <h2>📋 Panduan Penggunaan Lengkap</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; background: #f9f9f9; padding: 20px; border-radius: 8px;">
            <div>
                <h3>🎛️ Kontrol Section</h3>
                <ul>
                    <li><strong>Berita Terpanas:</strong> Diatur manual melalui menu "Berita Terpanas"</li>
                    <li><strong>Urutan Section:</strong> Dapat diatur melalui dropdown "Urutan Section Beranda"</li>
                    <li><strong>Video Section:</strong> Mendukung YouTube dan Facebook dengan autoplay opsional</li>
                    <li><strong>SEO Optimization:</strong> Autoplay video menggunakan lazy loading</li>
                </ul>

                <h3>📺 Video & Widget Management</h3>
                <ul>
                    <li><strong>Live Streaming:</strong> URL YouTube/Facebook di panel widget content</li>
                    <li><strong>Talkshow:</strong> Deskripsi dan informasi melalui panel admin</li>
                    <li><strong>Popular Posts:</strong> Jumlah post dapat diatur (3, 5, 7, atau 10)</li>
                    <li><strong>Widget Order:</strong> Urutan sidebar dapat diatur melalui dropdown</li>
                </ul>
            </div>

            <div>
                <h3>🎬 Video Embed Support</h3>
                <ul>
                    <li><strong>YouTube:</strong> https://youtube.com/watch?v=VIDEO_ID</li>
                    <li><strong>Facebook:</strong> https://facebook.com/username/videos/VIDEO_ID</li>
                    <li><strong>Responsive:</strong> Video otomatis menyesuaikan ukuran layar</li>
                    <li><strong>Performance:</strong> Lazy loading untuk performa optimal</li>
                </ul>

                <h3>⚙️ Template Options</h3>
                <ul>
                    <li><strong>Template Lama:</strong> Struktur section tetap (kompatibilitas)</li>
                    <li><strong>Template Baru:</strong> Section order dinamis + video embed</li>
                    <li><strong>Preview:</strong> Gunakan ?preview_template=v2 untuk test</li>
                    <li><strong>Sync:</strong> Semua pengaturan tersinkronisasi antar template</li>
                </ul>
            </div>
        </div>

        <div style="background: #fff3cd; padding: 15px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ffc107;">
            <h3>⚠️ Catatan Penting</h3>
            <ul>
                <li><strong>Video Autoplay:</strong> Akan menunggu konten lain selesai dimuat untuk SEO yang lebih baik</li>
                <li><strong>Facebook Video:</strong> Memerlukan Facebook SDK yang dimuat otomatis saat diperlukan</li>
                <li><strong>Widget Content:</strong> Semua konten widget dapat diatur melalui admin panel ini</li>
                <li><strong>Section Order:</strong> Perubahan urutan section hanya berlaku di template baru</li>
            </ul>
        </div>
    </div>
    <?php
}

// Admin page for hot news management
function bawana_hot_news_admin_page() {
    if (isset($_POST['save_hot_news']) && wp_verify_nonce($_POST['bawana_nonce'], 'bawana_hot_news')) {
        $hot_news_posts = array();
        for ($i = 1; $i <= 5; $i++) {
            if (!empty($_POST["hot_news_$i"])) {
                $hot_news_posts[] = intval($_POST["hot_news_$i"]);
            }
        }
        update_option('bawana_hot_news_posts', $hot_news_posts);
        echo '<div class="notice notice-success"><p>✅ Berita terpanas berhasil disimpan!</p></div>';
    }

    $hot_news_posts = get_option('bawana_hot_news_posts', array());
    ?>
    <div class="wrap">
        <h1>🔥 Pengaturan Berita Terpanas</h1>
        <p>Pilih maksimal 5 berita yang akan ditampilkan di section "Berita Terpanas" pada halaman beranda.</p>

        <form method="post" action="">
            <?php wp_nonce_field('bawana_hot_news', 'bawana_nonce'); ?>
            <table class="form-table">
                <?php for ($i = 1; $i <= 5; $i++) :
                    $selected_post = isset($hot_news_posts[$i-1]) ? $hot_news_posts[$i-1] : '';
                ?>
                <tr>
                    <th scope="row">Berita Terpanas #<?php echo $i; ?></th>
                    <td>
                        <div style="position: relative;">
                            <input type="text"
                                   id="hot_news_search_<?php echo $i; ?>"
                                   placeholder="Ketik untuk mencari berita..."
                                   style="width: 100%; padding: 8px; margin-bottom: 5px; border: 1px solid #ddd; border-radius: 4px;"
                                   onkeyup="filterHotNewsOptions(<?php echo $i; ?>)">
                            <select name="hot_news_<?php echo $i; ?>"
                                    id="hot_news_select_<?php echo $i; ?>"
                                    class="regular-text"
                                    style="width: 100%; max-height: 200px; overflow-y: auto;"
                                    onchange="updateSearchInput(<?php echo $i; ?>)">
                                <option value="">-- Pilih Berita --</option>
                                <?php
                                $posts = get_posts(array(
                                    'numberposts' => 200, // Increased for better search
                                    'post_status' => 'publish',
                                    'orderby' => 'date',
                                    'order' => 'DESC'
                                ));
                                foreach ($posts as $post) {
                                    $selected = ($selected_post == $post->ID) ? 'selected' : '';
                                    $post_title = esc_html($post->post_title);
                                    $post_date = get_the_date('d/m/Y', $post->ID);
                                    echo '<option value="' . $post->ID . '" ' . $selected . ' data-title="' . strtolower($post_title) . '">' . $post_title . ' (' . $post_date . ')</option>';
                                }
                                ?>
                            </select>
                        </div>
                        <?php if ($selected_post) : ?>
                            <br><small><a href="<?php echo get_edit_post_link($selected_post); ?>" target="_blank">Edit Post</a> | <a href="<?php echo get_permalink($selected_post); ?>" target="_blank">Lihat</a></small>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endfor; ?>
            </table>

            <?php submit_button('Simpan Berita Terpanas', 'primary', 'save_hot_news'); ?>
        </form>

        <script>
        function filterHotNewsOptions(index) {
            const searchInput = document.getElementById('hot_news_search_' + index);
            const select = document.getElementById('hot_news_select_' + index);
            const searchTerm = searchInput.value.toLowerCase();

            for (let i = 0; i < select.options.length; i++) {
                const option = select.options[i];
                const title = option.getAttribute('data-title') || option.text.toLowerCase();

                if (title.includes(searchTerm) || option.value === '') {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            }
        }

        function updateSearchInput(index) {
            const select = document.getElementById('hot_news_select_' + index);
            const searchInput = document.getElementById('hot_news_search_' + index);

            if (select.value) {
                const selectedOption = select.options[select.selectedIndex];
                searchInput.value = selectedOption.text;
            } else {
                searchInput.value = '';
            }
        }

        // Initialize search inputs with selected values
        document.addEventListener('DOMContentLoaded', function() {
            for (let i = 1; i <= 5; i++) {
                updateSearchInput(i);
            }
        });
        </script>

        <hr>
        <h2>📝 Preview Berita Terpanas Saat Ini</h2>
        <?php if (!empty($hot_news_posts)) : ?>
            <ol>
                <?php foreach ($hot_news_posts as $post_id) :
                    $post = get_post($post_id);
                    if ($post) :
                ?>
                    <li><strong><?php echo esc_html($post->post_title); ?></strong> - <a href="<?php echo get_edit_post_link($post_id); ?>">Edit</a> | <a href="<?php echo get_permalink($post_id); ?>" target="_blank">Lihat</a></li>
                <?php
                    endif;
                endforeach; ?>
            </ol>
        <?php else : ?>
            <p><em>Belum ada berita terpanas yang dipilih. Sistem akan menampilkan postingan terbaru secara otomatis.</em></p>
        <?php endif; ?>
    </div>
    <?php
}

/* ----------------------------------------------------------------------------
 * DISABLE PARENT THEME SETUP - Prevent conflicts
 */
remove_action('init', 'bawana_news_setup');

/* ----------------------------------------------------------------------------
 * FEATURED POST META BOX SYSTEM
 */

// Add meta box for featured posts
add_action('add_meta_boxes', 'bawana_add_featured_post_meta_box');
function bawana_add_featured_post_meta_box() {
    add_meta_box(
        'bawana_featured_post',
        '⭐ Postingan Unggulan',
        'bawana_featured_post_meta_box_callback',
        'post',
        'side',
        'high'
    );
}

// Meta box callback
function bawana_featured_post_meta_box_callback($post) {
    wp_nonce_field('bawana_featured_post_nonce', 'bawana_featured_post_nonce');
    $is_featured = get_post_meta($post->ID, '_bawana_featured_post', true);
    ?>
    <p>
        <label>
            <input type="checkbox" name="bawana_featured_post" value="1" <?php checked($is_featured, '1'); ?>>
            <strong>Tandai sebagai Postingan Unggulan</strong>
        </label>
    </p>
    <p><small>Postingan unggulan akan ditampilkan di section khusus pada halaman beranda.</small></p>
    <?php
}

// Save meta box data
add_action('save_post', 'bawana_save_featured_post_meta');
function bawana_save_featured_post_meta($post_id) {
    if (!isset($_POST['bawana_featured_post_nonce']) || !wp_verify_nonce($_POST['bawana_featured_post_nonce'], 'bawana_featured_post_nonce')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $is_featured = isset($_POST['bawana_featured_post']) ? '1' : '0';
    update_post_meta($post_id, '_bawana_featured_post', $is_featured);
}

// Add featured post column to admin posts list
add_filter('manage_posts_columns', 'bawana_add_featured_column');
function bawana_add_featured_column($columns) {
    $columns['featured'] = '⭐ Unggulan';
    return $columns;
}

add_action('manage_posts_custom_column', 'bawana_show_featured_column', 10, 2);
function bawana_show_featured_column($column, $post_id) {
    if ($column == 'featured') {
        $is_featured = get_post_meta($post_id, '_bawana_featured_post', true);
        echo $is_featured ? '⭐ Ya' : '-';
    }
}

/* ----------------------------------------------------------------------------
 * ENHANCED FAVICON FOR GOOGLE SEARCH VISIBILITY
 */

// Improve favicon for better Google search recognition
add_action('wp_head', 'bawana_enhanced_google_favicon', 1);
function bawana_enhanced_google_favicon() {
    $favicon_url = get_stylesheet_directory_uri() . '/images/favicon.png';
    $site_name = get_bloginfo('name');
    $site_url = home_url();

    // Enhanced favicon with proper MIME types and sizes for Google
    echo '<link rel="icon" type="image/png" sizes="16x16" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="icon" type="image/png" sizes="32x32" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="icon" type="image/png" sizes="48x48" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="icon" type="image/png" sizes="96x96" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="icon" type="image/png" sizes="144x144" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="icon" type="image/png" sizes="192x192" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="57x57" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="60x60" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="72x72" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="76x76" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="114x114" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="120x120" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="144x144" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="152x152" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="apple-touch-icon" sizes="180x180" href="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<link rel="shortcut icon" type="image/png" href="' . esc_url($favicon_url) . '" />' . "\n";

    // Microsoft tiles
    echo '<meta name="msapplication-TileImage" content="' . esc_url($favicon_url) . '" />' . "\n";
    echo '<meta name="msapplication-TileColor" content="#0A2E5B" />' . "\n";
    echo '<meta name="theme-color" content="#0A2E5B" />' . "\n";
    echo '<meta name="msapplication-navbutton-color" content="#0A2E5B" />' . "\n";
    echo '<meta name="apple-mobile-web-app-status-bar-style" content="#0A2E5B" />' . "\n";

    // Enhanced Schema.org structured data for Google
    echo '<script type="application/ld+json">' . "\n";
    echo '{' . "\n";
    echo '  "@context": "https://schema.org",' . "\n";
    echo '  "@type": "NewsMediaOrganization",' . "\n";
    echo '  "name": "' . esc_js($site_name) . '",' . "\n";
    echo '  "url": "' . esc_url($site_url) . '",' . "\n";
    echo '  "logo": {' . "\n";
    echo '    "@type": "ImageObject",' . "\n";
    echo '    "url": "' . esc_url($favicon_url) . '",' . "\n";
    echo '    "width": 192,' . "\n";
    echo '    "height": 192' . "\n";
    echo '  },' . "\n";
    echo '  "sameAs": [' . "\n";
    echo '    "' . esc_url($site_url) . '"' . "\n";
    echo '  ],' . "\n";
    echo '  "publishingPrinciples": "' . esc_url($site_url . '/pedoman-media-siber/') . '"' . "\n";
    echo '}' . "\n";
    echo '</script>' . "\n";

    // Additional meta for better search engine recognition
    echo '<meta name="application-name" content="' . esc_attr($site_name) . '" />' . "\n";
    echo '<meta name="msapplication-tooltip" content="' . esc_attr(get_bloginfo('description')) . '" />' . "\n";
    echo '<meta name="msapplication-starturl" content="' . esc_url($site_url) . '" />' . "\n";
}

/* ----------------------------------------------------------------------------
 * HELPER FUNCTION - Extract first image from post content with enhanced fallback
 */
function bawana_get_first_image($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    // Cek featured image dulu
    if (has_post_thumbnail($post_id)) {
        return get_the_post_thumbnail($post_id, 'medium');
    }

    // Jika tidak ada featured image, ambil gambar pertama dari konten
    $content = get_post_field('post_content', $post_id);
    $first_img = '';

    // Regex untuk mencari tag img
    preg_match_all('/<img.+src=[\'"]([^\'"]+)[\'"].*>/i', $content, $matches);

    if (!empty($matches[1][0])) {
        $first_img = '<img src="' . esc_url($matches[1][0]) . '" alt="' . esc_attr(get_the_title($post_id)) . '" class="first-content-image" style="width: 100%; height: auto; object-fit: cover;" />';
        return $first_img;
    }

    // Fallback ke placeholder theme
    $placeholder_url = get_template_directory_uri() . '/images/no-thumb/medium_large.png';
    return '<img src="' . esc_url($placeholder_url) . '" alt="' . esc_attr(get_the_title($post_id)) . '" class="placeholder-image" style="width: 100%; height: auto; object-fit: cover;" />';
}

/* ----------------------------------------------------------------------------
 * ENHANCED THUMBNAIL FUNCTION - dengan fallback ke gambar pertama dari konten
 */
function bawana_get_post_thumbnail($post_id = null, $size = 'medium', $class = '') {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    // Priority 1: Featured image
    if (has_post_thumbnail($post_id)) {
        return get_the_post_thumbnail($post_id, $size, array('class' => $class));
    }

    // Priority 2: First image from post content
    $content = get_post_field('post_content', $post_id);
    preg_match('/<img[^>]+src=[\'"]([^\'"]+)[\'"][^>]*>/i', $content, $matches);

    if (!empty($matches[1])) {
        $image_url = $matches[1];
        $alt_text = esc_attr(get_the_title($post_id));
        return '<img src="' . esc_url($image_url) . '" alt="' . $alt_text . '" class="' . esc_attr($class) . ' first-content-image" style="width: 100%; height: auto; object-fit: cover;">';
    }

    // Priority 3: Theme default placeholder
    $placeholder_url = get_template_directory_uri() . '/images/no-thumb/medium_large.png';
    $alt_text = esc_attr(get_the_title($post_id));
    return '<img src="' . esc_url($placeholder_url) . '" alt="' . $alt_text . '" class="' . esc_attr($class) . ' placeholder-image" style="width: 100%; height: auto; object-fit: cover;">';
}





// ============================================================================
// CUSTOM POST TYPE - ANGGOTA TIM / DEWAN REDAKSI
// ============================================================================

function bn_register_team_member_cpt() {
    $labels = array(
        'name'                  => _x( 'Anggota Tim', 'Post Type General Name', 'text_domain' ),
        'singular_name'         => _x( 'Anggota Tim', 'Post Type Singular Name', 'text_domain' ),
        'menu_name'             => __( 'Tim Redaksi', 'text_domain' ),
        'name_admin_bar'        => __( 'Anggota Tim', 'text_domain' ),
        'add_new_item'          => __( 'Tambah Anggota Baru', 'text_domain' ),
        'new_item'              => __( 'Anggota Baru', 'text_domain' ),
        'edit_item'             => __( 'Edit Anggota', 'text_domain' ),
        'view_item'             => __( 'Lihat Anggota', 'text_domain' ),
        'all_items'             => __( 'Semua Anggota', 'text_domain' ),
        'search_items'          => __( 'Cari Anggota', 'text_domain' ),
    );
    $args = array(
        'label'                 => __( 'Anggota Tim', 'text_domain' ),
        'description'           => __( 'Profil untuk anggota tim dan dewan redaksi', 'text_domain' ),
        'labels'                => $labels,
        'supports'              => array( 'title', 'editor', 'thumbnail' ),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-groups',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => 'dewan-redaksi', // URL: bawananews.com/dewan-redaksi
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'page',
        'rewrite'               => array('slug' => 'anggota'), // URL detail: bawananews.com/anggota/nama-anggota
    );
    register_post_type( 'team_member', $args );
}
add_action( 'init', 'bn_register_team_member_cpt', 0 );

// Flush rewrite rules setelah CPT dibuat agar URL berfungsi
function bn_flush_rewrite_rules_on_activate() {
    bn_register_team_member_cpt();
    flush_rewrite_rules();
}
register_activation_hook( __FILE__, 'bn_flush_rewrite_rules_on_activate' );

// ============================================================================
// ACF FIELDS SETUP UNTUK ANGGOTA TIM
// ============================================================================

// Setup ACF fields untuk team member
add_action('acf/init', 'bn_add_team_member_fields');
function bn_add_team_member_fields() {
    // Cek apakah ACF tersedia
    if( function_exists('acf_add_local_field_group') ):

        acf_add_local_field_group(array(
            'key' => 'group_team_member_info',
            'title' => 'Informasi Anggota Tim',
            'fields' => array(
                array(
                    'key' => 'field_jabatan',
                    'label' => 'Jabatan',
                    'name' => 'jabatan',
                    'type' => 'text',
                    'instructions' => 'Masukkan jabatan/posisi anggota tim (contoh: Pemimpin Redaksi, Jurnalis, dll)',
                    'required' => 1,
                    'default_value' => '',
                    'placeholder' => 'Contoh: Pemimpin Redaksi',
                ),
                array(
                    'key' => 'field_urutan_tampilan',
                    'label' => 'Urutan Tampilan',
                    'name' => 'urutan_tampilan',
                    'type' => 'number',
                    'instructions' => 'Isi dengan angka untuk mengurutkan posisi. Semakin kecil angkanya, semakin atas posisinya (Contoh: 1, 2, 3)',
                    'required' => 1,
                    'default_value' => 1,
                    'min' => 1,
                    'max' => 100,
                    'step' => 1,
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'team_member',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
        ));

    endif;
}

// ============================================================================
// ADMIN NOTICES & HELPER FUNCTIONS UNTUK TIM REDAKSI
// ============================================================================

// Admin notice untuk setup tim redaksi
add_action('admin_notices', 'bn_team_member_setup_notice');
function bn_team_member_setup_notice() {
    // Cek apakah ada anggota tim
    $team_count = wp_count_posts('team_member');
    $total_team = $team_count->publish;

    if ($total_team == 0 && current_user_can('manage_options')) {
        ?>
        <div class="notice notice-info is-dismissible">
            <p>
                <strong>Bawana News - Tim Redaksi:</strong>
                Belum ada anggota tim yang ditambahkan.
                <a href="<?php echo admin_url('post-new.php?post_type=team_member'); ?>">
                    Tambah Anggota Tim Sekarang
                </a>
            </p>
        </div>
        <?php
    }
}

// Tambahkan kolom custom di admin list
add_filter('manage_team_member_posts_columns', 'bn_team_member_columns');
function bn_team_member_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['jabatan'] = 'Jabatan';
    $new_columns['urutan'] = 'Urutan';
    $new_columns['thumbnail'] = 'Foto';
    $new_columns['date'] = $columns['date'];
    return $new_columns;
}

// Isi kolom custom di admin list
add_action('manage_team_member_posts_custom_column', 'bn_team_member_column_content', 10, 2);
function bn_team_member_column_content($column, $post_id) {
    switch ($column) {
        case 'jabatan':
            echo get_field('jabatan', $post_id) ?: '-';
            break;
        case 'urutan':
            echo get_field('urutan_tampilan', $post_id) ?: '-';
            break;
        case 'thumbnail':
            if (has_post_thumbnail($post_id)) {
                echo get_the_post_thumbnail($post_id, array(50, 50));
            } else {
                echo '<img src="' . get_stylesheet_directory_uri() . '/images/placeholder-avatar.svg" width="50" height="50" alt="No photo">';
            }
            break;
    }
}

// Buat kolom sortable
add_filter('manage_edit-team_member_sortable_columns', 'bn_team_member_sortable_columns');
function bn_team_member_sortable_columns($columns) {
    $columns['urutan'] = 'urutan_tampilan';
    return $columns;
}

// ============================================================================
// AUTO-CREATE TEAM MEMBERS DATA
// ============================================================================

// Fungsi untuk membuat data anggota tim otomatis (hanya dijalankan sekali)
add_action('init', 'bn_create_initial_team_members');
function bn_create_initial_team_members() {
    // Cek apakah sudah ada anggota tim
    $existing_team = get_posts(array(
        'post_type' => 'team_member',
        'posts_per_page' => 1,
        'post_status' => 'any'
    ));

    // Jika belum ada anggota tim, buat data awal
    if (empty($existing_team)) {
        $team_members = array(
            array(
                'name' => 'Saeful Mu\'minin',
                'jabatan' => 'Pimpinan Perusahaan',
                'urutan' => 1,
                'slug' => 'saeful-muminin'
            ),
            array(
                'name' => 'Babussalam',
                'jabatan' => 'Pimpinan Redaksi',
                'urutan' => 2,
                'slug' => 'babussalam'
            ),
            array(
                'name' => 'Darsono',
                'jabatan' => 'Pimpinan Umum',
                'urutan' => 3,
                'slug' => 'darsono'
            ),
            array(
                'name' => 'Moh Mujiburrahman',
                'jabatan' => 'IT Support',
                'urutan' => 4,
                'slug' => 'moh-mujiburrahman'
            ),
            array(
                'name' => 'Faizal Fardan',
                'jabatan' => 'Redaktur',
                'urutan' => 5,
                'slug' => 'faizal-fardan'
            ),
            array(
                'name' => 'Kevin Fernando',
                'jabatan' => 'Redaktur',
                'urutan' => 6,
                'slug' => 'kevin-fernando'
            ),
            array(
                'name' => 'Mohammad Faidin',
                'jabatan' => 'Jurnalis',
                'urutan' => 7,
                'slug' => 'mohammad-faidin-1' // URL baru untuk Faidin
            ),
            array(
                'name' => 'Abdul Khodir',
                'jabatan' => 'Jurnalis',
                'urutan' => 8,
                'slug' => 'abdul-khodir'
            ),
            array(
                'name' => 'M. Sholeh',
                'jabatan' => 'Wartawan',
                'urutan' => 9,
                'slug' => 'mohammad-faidin' // Menggunakan URL lama Faidin untuk QR code
            )
        );

        foreach ($team_members as $member) {
            // Buat post baru dengan slug custom
            $post_id = wp_insert_post(array(
                'post_title' => $member['name'],
                'post_name' => $member['slug'], // Set custom slug
                'post_content' => 'Profil lengkap akan segera ditambahkan.',
                'post_status' => 'publish',
                'post_type' => 'team_member',
                'post_author' => 1
            ));

            // Tambahkan custom fields jika post berhasil dibuat
            if ($post_id && !is_wp_error($post_id)) {
                update_field('jabatan', $member['jabatan'], $post_id);
                update_field('urutan_tampilan', $member['urutan'], $post_id);
            }
        }
    }
}

// ============================================================================
// FUNGSI UPDATE DATA TIM YANG SUDAH ADA
// ============================================================================

// Fungsi untuk update data tim yang sudah ada (manual trigger)
function bn_update_existing_team_members() {
    // Hapus semua anggota tim yang ada
    $existing_posts = get_posts(array(
        'post_type' => 'team_member',
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    foreach ($existing_posts as $post) {
        wp_delete_post($post->ID, true); // Force delete
    }

    // Buat ulang dengan data yang benar - UPDATE 2024
    $team_members = array(
        array(
            'name' => 'Saeful Mu\'minin',
            'jabatan' => 'Pimpinan Perusahaan',
            'urutan' => 1,
            'slug' => 'saeful-muminin'
        ),
        array(
            'name' => 'Babussalam',
            'jabatan' => 'Pimpinan Redaksi',
            'urutan' => 2,
            'slug' => 'babussalam'
        ),
        array(
            'name' => 'Darsono',
            'jabatan' => 'Pimpinan Umum',
            'urutan' => 3,
            'slug' => 'darsono'
        ),
        array(
            'name' => 'Moh Mujiburrahman',
            'jabatan' => 'IT Support',
            'urutan' => 4,
            'slug' => 'moh-mujiburrahman'
        ),
        array(
            'name' => 'Faizal Fardan',
            'jabatan' => 'Redaktur',
            'urutan' => 5,
            'slug' => 'faizal-fardan'
        ),
        array(
            'name' => 'Kevin Fernando',
            'jabatan' => 'Redaktur',
            'urutan' => 5,
            'slug' => 'kevin-fernando'
        ),
        // ANGGOTA BARU 1: Mukhammad Naufal - Manager Eksternal (Urutan: 6)
        array(
            'name' => 'Mukhammad Naufal',
            'jabatan' => 'Manager Eksternal',
            'urutan' => 6,
            'slug' => 'mukhammad-naufal'
        ),
        // ANGGOTA BARU 2: Abdurrahman Asy'ari - Wakil Manager Eksternal (Urutan: 6)
        array(
            'name' => 'Abdurrahman Asy\'ari',
            'jabatan' => 'Wakil Manager Eksternal',
            'urutan' => 6,
            'slug' => 'abdurrahman-asyari'
        ),
        // UPDATE: Mohammad Faidin - Jurnalis (Urutan: 7)
        array(
            'name' => 'Mohammad Faidin',
            'jabatan' => 'Jurnalis',
            'urutan' => 7,
            'slug' => 'mohammad-faidin-1' // URL baru untuk Faidin
        ),
        // UPDATE: Abdul Khodir - Jurnalis (Urutan: 7)
        array(
            'name' => 'Abdul Khodir',
            'jabatan' => 'Jurnalis',
            'urutan' => 7,
            'slug' => 'abdul-khodir'
        ),
        // ANGGOTA BARU 3: M. Sholeh - Wartawan (Urutan: 7)
        array(
            'name' => 'M. Sholeh',
            'jabatan' => 'Wartawan',
            'urutan' => 7,
            'slug' => 'mohammad-faidin' // Menggunakan URL lama Faidin untuk QR code
        )
    );

    foreach ($team_members as $member) {
        // Buat post baru dengan slug custom
        $post_id = wp_insert_post(array(
            'post_title' => $member['name'],
            'post_name' => $member['slug'], // Set custom slug
            'post_content' => 'Profil lengkap akan segera ditambahkan.',
            'post_status' => 'publish',
            'post_type' => 'team_member',
            'post_author' => 1
        ));

        // Tambahkan custom fields jika post berhasil dibuat
        if ($post_id && !is_wp_error($post_id)) {
            update_field('jabatan', $member['jabatan'], $post_id);
            update_field('urutan_tampilan', $member['urutan'], $post_id);
        }
    }

    // Flush rewrite rules setelah update
    flush_rewrite_rules();

    return "Data tim berhasil diupdate!";
}

// Fungsi untuk menjalankan update tim redaksi 2024
add_action('init', 'bn_run_team_update_2024');
function bn_run_team_update_2024() {
    // Cek apakah update sudah pernah dijalankan
    $update_done = get_option('bn_team_update_2024_done', false);

    if (!$update_done) {
        // Jalankan update
        bn_update_existing_team_members();

        // Tandai bahwa update sudah selesai
        update_option('bn_team_update_2024_done', true);
    }
}

// Update data tim sudah selesai - dinonaktifkan
// add_action('init', 'bn_update_existing_team_members', 999);

// ============================================================================
// ADMIN NOTICE UNTUK KONFIRMASI UPDATE
// ============================================================================

// Admin notice untuk konfirmasi update data tim
add_action('admin_notices', 'bn_team_update_success_notice');
function bn_team_update_success_notice() {
    if (current_user_can('manage_options')) {
        $team_count = wp_count_posts('team_member');
        $total_team = $team_count->publish;

        if ($total_team >= 9) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p>
                    <strong>✅ Bawana News - Tim Redaksi:</strong>
                    Data anggota tim berhasil diupdate! Total <?php echo $total_team; ?> anggota.
                    <a href="<?php echo admin_url('edit.php?post_type=team_member'); ?>">
                        Lihat Semua Anggota
                    </a> |
                    <a href="<?php echo home_url('/dewan-redaksi'); ?>" target="_blank">
                        Lihat Halaman Tim
                    </a>
                </p>
                <p><strong>URL Khusus:</strong></p>
                <ul>
                    <li>M. Sholeh: <code>/anggota/mohammad-faidin</code> (untuk QR code lama)</li>
                    <li>Mohammad Faidin: <code>/anggota/mohammad-faidin-1</code> (URL baru)</li>
                </ul>
            </div>
            <?php
        }
    }
}



// Quick Edit untuk Featured Posts
function bawana_quick_edit_featured() {
    ?>
    <fieldset class="inline-edit-col-right">
        <div class="inline-edit-col">
            <label>
                <input type="checkbox" name="bawana_featured_post" value="1" />
                <span class="checkbox-title">Postingan Unggulan</span>
            </label>
        </div>
    </fieldset>
    <script>
    jQuery(document).ready(function($) {
        $('a.editinline').on('click', function() {
            var post_id = $(this).closest('tr').attr('id').replace('post-', '');
            var featured = $(this).closest('tr').find('.column-featured').text().indexOf('★') !== -1;
            $('input[name="bawana_featured_post"]').prop('checked', featured);
        });
    });
    </script>
    <?php
}
add_action('quick_edit_custom_box', 'bawana_quick_edit_featured', 10, 2);

/* ----------------------------------------------------------------------------
 * Bawana News Child Theme Setup - Kustomisasi untuk website Bawana News
 */
add_action('wp_loaded', 'bawana_news_child_setup', 5);
function bawana_news_child_setup() {
    // Hanya jalankan sekali saat tema diaktifkan
    if (get_option('bawana_news_child_setup_done')) {
        return;
    }

    // 1. Buat halaman-halaman statis yang diperlukan
    $pages_to_create = array(
        'Redaksi' => 'redaksi',
        'Kode Etik Jurnalistik' => 'kode-etik-jurnalistik',
        'Kontak' => 'kontak',
        'Tentang Kami' => 'tentang-kami',
        'Pedoman Media Siber' => 'pedoman-media-siber',
        'Advertise' => 'advertise'
    );

    $created_pages = array();
    foreach ($pages_to_create as $title => $slug) {
        // Cek apakah halaman sudah ada
        $existing_page = get_page_by_path($slug);
        if (!$existing_page) {
            $page_id = wp_insert_post(array(
                'post_title' => $title,
                'post_name' => $slug,
                'post_content' => '<p>Konten untuk halaman ' . $title . ' akan ditambahkan di sini.</p>',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1
            ));
            $created_pages[$slug] = $page_id;
        } else {
            $created_pages[$slug] = $existing_page->ID;
        }
    }

    // 2. Buat kategori untuk menu header jika belum ada
    $categories_to_create = array('Nasional', 'Internasional', 'Olahraga', 'Ekonomi', 'Teknologi');
    foreach ($categories_to_create as $cat_name) {
        if (!term_exists($cat_name, 'category')) {
            wp_insert_term($cat_name, 'category');
        }
    }

    // 3. Buat menu Header Utama
    $header_menu_name = 'Header Utama';
    $header_menu = wp_get_nav_menu_object($header_menu_name);
    if (!$header_menu) {
        $header_menu_id = wp_create_nav_menu($header_menu_name);

        // Tambahkan kategori ke menu header
        foreach ($categories_to_create as $cat_name) {
            $category = get_term_by('name', $cat_name, 'category');
            if ($category) {
                wp_update_nav_menu_item($header_menu_id, 0, array(
                    'menu-item-title' => $cat_name,
                    'menu-item-object' => 'category',
                    'menu-item-object-id' => $category->term_id,
                    'menu-item-type' => 'taxonomy',
                    'menu-item-status' => 'publish'
                ));
            }
        }

        // Assign menu ke location
        $locations = get_theme_mod('nav_menu_locations');
        $locations['header-menu'] = $header_menu_id;
        set_theme_mod('nav_menu_locations', $locations);
    }

    // 4. Buat menu Footer Informasi
    $footer_menu_name = 'Footer Informasi';
    $footer_menu = wp_get_nav_menu_object($footer_menu_name);
    if (!$footer_menu) {
        $footer_menu_id = wp_create_nav_menu($footer_menu_name);

        // Tambahkan halaman ke menu footer
        foreach ($created_pages as $slug => $page_id) {
            wp_update_nav_menu_item($footer_menu_id, 0, array(
                'menu-item-title' => get_the_title($page_id),
                'menu-item-object' => 'page',
                'menu-item-object-id' => $page_id,
                'menu-item-type' => 'post_type',
                'menu-item-status' => 'publish'
            ));
        }

        // Assign menu ke location
        $locations = get_theme_mod('nav_menu_locations');
        $locations['footer-menu'] = $footer_menu_id;
        set_theme_mod('nav_menu_locations', $locations);
    }

    // 5. Update site title dan tagline untuk Bawana News
    update_option('blogname', 'Bawana News');
    update_option('blogdescription', 'Mendunia');

    // 6. Buat beberapa postingan dummy jika belum ada postingan
    $post_count = wp_count_posts()->publish;
    if ($post_count < 10) {
        $dummy_posts = array(
            array(
                'title' => 'AKHIRNYA NGAKU JUGA! Setelah \'Dihajar\' Amerika, Iran Jujur Fasilitas Nuklirnya Rusak Parah.',
                'content' => 'Iran akhirnya mengakui bahwa fasilitas nuklir mereka mengalami kerusakan serius setelah serangan yang dilakukan Amerika Serikat. Pengakuan ini datang setelah berbulan-bulan penyangkalan dari pihak Iran.',
                'category' => 'Internasional'
            ),
            array(
                'title' => 'KOK BISA? Merek Legendaris \'DEL MONTE\' Tiba-tiba Bangkrut di Amerika! Bapak-bapak Pasti Cemas, Gimana Nasib Produknya di Indonesia?',
                'content' => 'Merek legendaris Del Monte mengalami kebangkrutan di Amerika Serikat, menimbulkan kekhawatiran tentang nasib produk-produk mereka di Indonesia.',
                'category' => 'Ekonomi'
            ),
            array(
                'title' => 'Masih Ingat \'Papa\'? Hukuman Setya Novanto di Kasus E-KTP Tiba-tiba \'Dapat Diskon\' dari MA. Ada Apa Ini?',
                'content' => 'Mahkamah Agung memberikan keringanan hukuman kepada Setya Novanto dalam kasus E-KTP yang mengejutkan banyak pihak.',
                'category' => 'Nasional'
            ),
            array(
                'title' => 'GEBRAKAN KAPOLRI! Ternyata Bukan Cuma Urus Keamanan, Polri Kini \'Panen\' Jutaan Ton Jagung & Sukses Ekspor. Siap Jadi Lumbung Pangan Nasional!',
                'content' => 'Kapolri meluncurkan program pertanian yang berhasil menghasilkan jutaan ton jagung dan sukses ekspor, menjadikan Polri sebagai kontributor ketahanan pangan nasional.',
                'category' => 'Nasional'
            ),
            array(
                'title' => 'GEGER! Sosok Kontroversial Ini Kembali ke Indonesia, Bapak-bapak Wajib Tahu Jadwal Lengkapnya di 4 Kota Besar Ini!',
                'content' => 'Sosok kontroversial yang sempat menjadi perbincangan publik kembali ke Indonesia dengan jadwal kunjungan di empat kota besar.',
                'category' => 'Nasional'
            )
        );

        foreach ($dummy_posts as $dummy_post) {
            // Cek kategori
            $category = get_term_by('name', $dummy_post['category'], 'category');
            if (!$category) {
                $category_id = wp_insert_term($dummy_post['category'], 'category');
                $category_id = $category_id['term_id'];
            } else {
                $category_id = $category->term_id;
            }

            // Buat postingan
            $post_id = wp_insert_post(array(
                'post_title' => $dummy_post['title'],
                'post_content' => $dummy_post['content'],
                'post_status' => 'publish',
                'post_type' => 'post',
                'post_author' => 1,
                'post_category' => array($category_id)
            ));

            // Set featured image placeholder jika ada
            if ($post_id && !has_post_thumbnail($post_id)) {
                // Buat placeholder image URL
                $placeholder_url = 'https://via.placeholder.com/600x400/C9A961/FFFFFF?text=' . urlencode(substr($dummy_post['title'], 0, 20));
                // Untuk demo, kita skip featured image karena butuh upload
            }
        }
    }

    // Tandai bahwa setup sudah selesai
    update_option('bawana_news_child_setup_done', true);
}

/* ----------------------------------------------------------------------------
 * Custom Footer Copyright
 */
add_filter('wp_footer', 'bawana_news_custom_footer_script');
function bawana_news_custom_footer_script() {
    ?>
    <script>
    jQuery(document).ready(function($) {
        // Update copyright text jika masih menggunakan default
        $('.td-sub-footer-copy').html('&copy; <?php echo date("Y"); ?> Bawana News. All rights reserved.');
    });
    </script>
    <?php
}

/* ----------------------------------------------------------------------------
 * Bawana News - Slot Konten Terpusat untuk Sidebar
 * Membuat sistem mudah untuk operator mengelola Live Streaming dan Talkshow
 */

// Tambahkan custom options untuk slot konten
add_action('admin_init', 'bawana_news_add_content_slots');
function bawana_news_add_content_slots() {
    // Slot Live Streaming
    add_option('bawana_live_streaming_content', '');
    add_option('bawana_live_streaming_title', 'LIVE SEKARANG');

    // Slot Talkshow
    add_option('bawana_talkshow_content', '');
    add_option('bawana_talkshow_title', 'BAWANA TALKS');
}

// Tambahkan menu admin untuk mengelola slot konten
add_action('admin_menu', 'bawana_news_admin_menu');
function bawana_news_admin_menu() {
    add_submenu_page(
        'td_theme_welcome', // Parent menu (Theme Panel)
        'Kelola Video Sidebar',
        'Video Sidebar',
        'edit_posts',
        'bawana-video-sidebar',
        'bawana_news_video_sidebar_page'
    );

    // Tambahkan submenu untuk troubleshooting widget
    add_submenu_page(
        'td_theme_welcome',
        'Widget Troubleshooting',
        '🔧 Widget Troubleshooting',
        'manage_options',
        'bawana-widget-troubleshooting',
        'bawana_widget_troubleshooting_page'
    );
}

// Halaman admin untuk mengelola slot konten
function bawana_news_video_sidebar_page() {
    // Simpan data jika form disubmit
    if (isset($_POST['submit'])) {
        if (wp_verify_nonce($_POST['bawana_nonce'], 'bawana_video_sidebar')) {
            update_option('bawana_live_streaming_content', sanitize_textarea_field($_POST['live_streaming_content']));
            update_option('bawana_live_streaming_title', sanitize_text_field($_POST['live_streaming_title']));
            update_option('bawana_talkshow_content', sanitize_textarea_field($_POST['talkshow_content']));
            update_option('bawana_talkshow_title', sanitize_text_field($_POST['talkshow_title']));
            echo '<div class="notice notice-success"><p>Video sidebar berhasil diperbarui!</p></div>';
        }
    }

    // Ambil data yang tersimpan
    $live_content = get_option('bawana_live_streaming_content', '');
    $live_title = get_option('bawana_live_streaming_title', 'LIVE SEKARANG');
    $talkshow_content = get_option('bawana_talkshow_content', '');
    $talkshow_title = get_option('bawana_talkshow_title', 'BAWANA TALKS');
    ?>
    <div class="wrap">
        <h1>🎥 Kelola Video Sidebar - Bawana News</h1>
        <p><strong>Panduan Mudah:</strong> Copy kode embed video dari YouTube/Facebook, lalu paste di kotak di bawah ini.</p>

        <form method="post" action="">
            <?php wp_nonce_field('bawana_video_sidebar', 'bawana_nonce'); ?>

            <div style="background: #fff; padding: 20px; margin: 20px 0; border-left: 4px solid #1e3a8a;">
                <h2>📺 Live Streaming</h2>
                <p>Untuk mengganti video live streaming, copy kode embed dari YouTube atau Facebook:</p>
                <table class="form-table">
                    <tr>
                        <th scope="row">Judul</th>
                        <td><input type="text" name="live_streaming_title" value="<?php echo esc_attr($live_title); ?>" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th scope="row">Kode Embed Video</th>
                        <td>
                            <textarea name="live_streaming_content" rows="6" cols="80" placeholder="Paste kode embed video di sini..."><?php echo esc_textarea($live_content); ?></textarea>
                            <p class="description">Contoh: &lt;iframe src="https://www.youtube.com/embed/VIDEO_ID" width="560" height="315"&gt;&lt;/iframe&gt;</p>
                        </td>
                    </tr>
                </table>
            </div>

            <div style="background: #fff; padding: 20px; margin: 20px 0; border-left: 4px solid #d4af37;">
                <h2>🎙️ Talkshow</h2>
                <p>Untuk mengganti video talkshow, copy kode embed dari YouTube atau Facebook:</p>
                <table class="form-table">
                    <tr>
                        <th scope="row">Judul</th>
                        <td><input type="text" name="talkshow_title" value="<?php echo esc_attr($talkshow_title); ?>" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th scope="row">Kode Embed Video</th>
                        <td>
                            <textarea name="talkshow_content" rows="6" cols="80" placeholder="Paste kode embed video di sini..."><?php echo esc_textarea($talkshow_content); ?></textarea>
                            <p class="description">Contoh: &lt;iframe src="https://www.facebook.com/plugins/video.php?href=..." width="560" height="315"&gt;&lt;/iframe&gt;</p>
                        </td>
                    </tr>
                </table>
            </div>

            <?php submit_button('💾 Simpan Perubahan', 'primary', 'submit', false, array('style' => 'font-size: 16px; padding: 10px 20px;')); ?>
        </form>

        <div style="background: #f0f8ff; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h3>📋 Cara Mendapatkan Kode Embed:</h3>
            <ul>
                <li><strong>YouTube:</strong> Buka video → Klik "Share" → Klik "Embed" → Copy kode yang muncul</li>
                <li><strong>Facebook:</strong> Buka video → Klik "..." → Pilih "Embed" → Copy kode yang muncul</li>
            </ul>
        </div>
    </div>
    <?php
}

// Halaman Troubleshooting Widget
function bawana_widget_troubleshooting_page() {
    // Handle reset widgets
    if (isset($_POST['reset_widgets']) && wp_verify_nonce($_POST['bawana_nonce'], 'bawana_widget_troubleshooting')) {
        delete_option('bawana_sidebar_setup_done');
        bawana_news_setup_sidebar();
        echo '<div class="notice notice-success"><p>✅ Widget berhasil direset dan diatur ulang!</p></div>';
    }

    // Handle force refresh
    if (isset($_POST['force_refresh']) && wp_verify_nonce($_POST['bawana_nonce'], 'bawana_widget_troubleshooting')) {
        // Clear all widget caches
        wp_cache_delete('sidebars_widgets', 'options');
        wp_cache_delete('widget_bawana_live_streaming', 'options');
        wp_cache_delete('widget_bawana_popular_posts', 'options');
        wp_cache_delete('widget_bawana_talkshow', 'options');
        echo '<div class="notice notice-success"><p>✅ Cache widget berhasil dibersihkan!</p></div>';
    }

    $sidebars_widgets = get_option('sidebars_widgets', array());
    $live_content = get_option('bawana_live_streaming_content', '');
    $talkshow_content = get_option('bawana_talkshow_content', '');
    ?>
    <div class="wrap">
        <h1>🔧 Widget Troubleshooting - Bawana News</h1>
        <p>Halaman ini membantu mendiagnosis dan memperbaiki masalah widget yang tidak muncul.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-left: 4px solid #1e3a8a;">
            <h2>📊 Status Widget Saat Ini</h2>

            <h3>Widget yang Terdaftar di Sidebar:</h3>
            <?php if (isset($sidebars_widgets['td-default']) && !empty($sidebars_widgets['td-default'])): ?>
                <ul>
                    <?php foreach ($sidebars_widgets['td-default'] as $widget): ?>
                        <li><code><?php echo esc_html($widget); ?></code></li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p style="color: red;">❌ Tidak ada widget yang terdaftar di sidebar 'td-default'</p>
            <?php endif; ?>

            <h3>Status Konten Widget:</h3>
            <ul>
                <li><strong>Live Streaming:</strong> <?php echo !empty($live_content) ? '✅ Ada konten' : '❌ Belum ada konten'; ?></li>
                <li><strong>Talkshow:</strong> <?php echo !empty($talkshow_content) ? '✅ Ada konten' : '❌ Belum ada konten'; ?></li>
                <li><strong>Popular Posts:</strong> ✅ Otomatis (menggunakan post yang ada)</li>
            </ul>
        </div>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-left: 4px solid #d4af37;">
            <h2>🛠️ Perbaikan Otomatis</h2>
            <p>Jika widget tidak muncul, coba langkah-langkah berikut:</p>

            <form method="post" action="" style="margin: 15px 0;">
                <?php wp_nonce_field('bawana_widget_troubleshooting', 'bawana_nonce'); ?>
                <button type="submit" name="reset_widgets" class="button button-primary" style="margin-right: 10px;">
                    🔄 Reset & Setup Ulang Widget
                </button>
                <button type="submit" name="force_refresh" class="button button-secondary">
                    🗑️ Bersihkan Cache Widget
                </button>
            </form>
        </div>

        <div style="background: #f0f8ff; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h3>📋 Langkah Manual Jika Masalah Berlanjut:</h3>
            <ol>
                <li>Pergi ke <strong>Appearance → Widgets</strong></li>
                <li>Cari widget berikut di daftar Available Widgets:
                    <ul>
                        <li>Bawana Live Streaming</li>
                        <li>Bawana Berita Populer</li>
                        <li>Bawana Talkshow</li>
                    </ul>
                </li>
                <li>Drag widget tersebut ke sidebar <strong>"Newspaper default"</strong></li>
                <li>Klik Save</li>
            </ol>
        </div>

        <div style="background: #fff3cd; padding: 15px; margin: 20px 0; border-radius: 5px; border: 1px solid #ffeaa7;">
            <h3>⚠️ Catatan Penting:</h3>
            <ul>
                <li>Widget akan menampilkan placeholder jika belum ada konten</li>
                <li>Untuk Live Streaming dan Talkshow, tambahkan konten melalui <a href="<?php echo admin_url('admin.php?page=bawana-video-sidebar'); ?>">Video Sidebar</a></li>
                <li>Widget Popular Posts akan otomatis menampilkan post terbaru jika belum ada post dengan komentar</li>
            </ul>
        </div>

        <div style="background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h3>✅ Production Ready</h3>
            <p>Widget troubleshooting telah dioptimasi untuk production. Debug mode dinonaktifkan untuk performa terbaik.</p>
        </div>
    </div>
    <?php
}

/* ----------------------------------------------------------------------------
 * Widget Custom untuk Live Streaming dan Talkshow
 */

// Widget Live Streaming
class Bawana_Live_Streaming_Widget extends WP_Widget {
    function __construct() {
        parent::__construct(
            'bawana_live_streaming',
            'Bawana Live Streaming',
            array('description' => 'Menampilkan video live streaming dari slot konten terpusat')
        );
    }

    public function widget($args, $instance) {
        $title = get_option('bawana_live_streaming_title', 'LIVE SEKARANG');
        $url = get_option('bawana_live_streaming_url', '');
        $description = get_option('bawana_live_streaming_description', 'Video live streaming akan muncul di sini.');

        echo $args['before_widget'];
        if (!empty($title)) {
            echo $args['before_title'] . $title . $args['after_title'];
        }

        if (!empty($url)) {
            // Parse video URL untuk embed
            $embed_code = $this->get_video_embed($url);
            if ($embed_code) {
                echo '<div class="bawana-video-container">' . $embed_code . '</div>';
            } else {
                echo '<div class="bawana-video-link">';
                echo '<p><a href="' . esc_url($url) . '" target="_blank" style="display: inline-block; padding: 10px 15px; background: #007cba; color: white; text-decoration: none; border-radius: 5px;">🔗 Tonton Live Streaming</a></p>';
                echo '</div>';
            }
        }

        if (!empty($description)) {
            echo '<div class="bawana-video-description">';
            echo '<p style="margin-top: 10px; color: #666; font-size: 14px;">' . esc_html($description) . '</p>';
            echo '</div>';
        }
        // Jika tidak ada URL, tampilkan placeholder
        if (empty($url)) {
            echo '<div class="bawana-video-placeholder">';
            echo '<p style="text-align: center; padding: 20px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; color: #6c757d;">';
            echo '📺 Video live streaming akan muncul di sini.<br>';
            echo '<small>Kelola dari <a href="' . admin_url('admin.php?page=bawana-beranda') . '">Admin Panel → Beranda</a></small>';
            echo '</p>';
            echo '</div>';
        }

        echo $args['after_widget'];
    }

    private function get_video_embed($url) {
        // YouTube embed
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $video_id = $matches[1];
            return '<iframe width="100%" height="200" src="https://www.youtube.com/embed/' . $video_id . '?rel=0&modestbranding=1" frameborder="0" allowfullscreen loading="lazy" style="border-radius: 8px;"></iframe>';
        }

        // Facebook embed
        if (strpos($url, 'facebook.com') !== false) {
            return '<div class="fb-video" data-href="' . esc_url($url) . '" data-width="auto" data-show-text="false" data-allowfullscreen="true"></div>';
        }

        return false;
    }

    public function form($instance) {
        echo '<p>Video dikelola dari <a href="' . admin_url('admin.php?page=bawana-video-sidebar') . '">Video Sidebar</a> di Theme Panel.</p>';
    }
}

// Widget Talkshow
class Bawana_Talkshow_Widget extends WP_Widget {
    function __construct() {
        parent::__construct(
            'bawana_talkshow',
            'Bawana Talkshow',
            array('description' => 'Menampilkan video talkshow dari slot konten terpusat')
        );
    }

    public function widget($args, $instance) {
        $title = get_option('bawana_talkshow_title', 'TALKSHOW');
        $description = get_option('bawana_talkshow_description', 'Informasi talkshow akan muncul di sini.');

        echo $args['before_widget'];
        if (!empty($title)) {
            echo $args['before_title'] . $title . $args['after_title'];
        }

        if (!empty($description)) {
            echo '<div class="bawana-talkshow-content">';
            echo '<div style="padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007cba;">';
            echo '<p style="margin: 0; color: #333; line-height: 1.6;">' . nl2br(esc_html($description)) . '</p>';
            echo '</div>';
            echo '</div>';
        } else {
            // Tampilkan placeholder jika belum ada konten
            echo '<div class="bawana-talkshow-placeholder">';
            echo '<p style="text-align: center; padding: 20px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; color: #6c757d;">';
            echo '🎙️ Informasi talkshow akan muncul di sini.<br>';
            echo '<small>Kelola dari <a href="' . admin_url('admin.php?page=bawana-beranda') . '">Admin Panel → Beranda</a></small>';
            echo '</p>';
            echo '</div>';
        }
        echo $args['after_widget'];
    }

    public function form($instance) {
        echo '<p>Video dikelola dari <a href="' . admin_url('admin.php?page=bawana-video-sidebar') . '">Video Sidebar</a> di Theme Panel.</p>';
    }
}

// Widget Popular Posts
class Bawana_Popular_Posts_Widget extends WP_Widget {
    function __construct() {
        parent::__construct(
            'bawana_popular_posts',
            'Bawana Berita Populer',
            array('description' => 'Menampilkan 5 berita paling populer berdasarkan views')
        );
    }

    public function widget($args, $instance) {
        $title = get_option('bawana_popular_posts_title', 'BERITA POPULER');
        $number = get_option('bawana_popular_posts_count', 5);

        echo $args['before_widget'];
        echo $args['before_title'] . $title . $args['after_title'];
        echo '<div class="bawana-popular-posts">';

        // Query untuk mendapatkan post populer berdasarkan comment count sebagai proxy untuk popularity
        $popular_posts = new WP_Query(array(
            'post_type' => 'post',
            'posts_per_page' => $number,
            'orderby' => 'comment_count',
            'order' => 'DESC',
            'meta_query' => array(
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'EXISTS'
                )
            )
        ));

        if ($popular_posts->have_posts()) {
            $counter = 1;
            while ($popular_posts->have_posts()) {
                $popular_posts->the_post();
                $thumbnail = bawana_get_post_thumbnail(get_the_ID(), 'thumbnail', 'popular-thumb');
                ?>
                <div class="popular-post-item">
                    <div class="popular-number"><?php echo $counter; ?></div>
                    <div class="popular-thumb">
                        <a href="<?php the_permalink(); ?>">
                            <?php echo $thumbnail; ?>
                        </a>
                    </div>
                    <div class="popular-content">
                        <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                        <div class="popular-meta">
                            <span class="popular-date"><?php echo get_the_date('d M Y'); ?></span>
                            <span class="popular-comments"><?php echo get_comments_number(); ?> komentar</span>
                        </div>
                    </div>
                </div>
                <?php
                $counter++;
            }
            wp_reset_postdata();
        } else {
            // Jika tidak ada post dengan komentar, ambil post terbaru dengan featured image
            $recent_posts = new WP_Query(array(
                'post_type' => 'post',
                'posts_per_page' => $number,
                'orderby' => 'date',
                'order' => 'DESC',
                'meta_query' => array(
                    array(
                        'key' => '_thumbnail_id',
                        'compare' => 'EXISTS'
                    )
                )
            ));

            if ($recent_posts->have_posts()) {
                $counter = 1;
                while ($recent_posts->have_posts()) {
                    $recent_posts->the_post();
                    $thumbnail = bawana_get_post_thumbnail(get_the_ID(), 'thumbnail', 'popular-thumb');
                    ?>
                    <div class="popular-post-item">
                        <div class="popular-number"><?php echo $counter; ?></div>
                        <div class="popular-thumb">
                            <a href="<?php the_permalink(); ?>">
                                <?php echo $thumbnail; ?>
                            </a>
                        </div>
                        <div class="popular-content">
                            <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                            <div class="popular-meta">
                                <span class="popular-date"><?php echo get_the_date('d M Y'); ?></span>
                                <span class="popular-comments">Berita Terbaru</span>
                            </div>
                        </div>
                    </div>
                    <?php
                    $counter++;
                }
                wp_reset_postdata();
            } else {
                // Jika sama sekali tidak ada post dengan featured image
                echo '<div class="bawana-popular-placeholder">';
                echo '<p style="text-align: center; padding: 20px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; color: #6c757d;">';
                echo '📰 Berita populer akan muncul di sini setelah ada postingan dengan gambar unggulan.';
                echo '</p>';
                echo '</div>';
            }
        }

        echo '</div>';
        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'BERITA POPULER';
        $number = !empty($instance['number']) ? $instance['number'] : 5;
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Judul:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('number'); ?>">Jumlah Berita:</label>
            <input class="tiny-text" id="<?php echo $this->get_field_id('number'); ?>" name="<?php echo $this->get_field_name('number'); ?>" type="number" step="1" min="1" max="10" value="<?php echo esc_attr($number); ?>">
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        $instance['number'] = (!empty($new_instance['number'])) ? absint($new_instance['number']) : 5;
        return $instance;
    }
}

// Widget Ads untuk Bawana News
class Bawana_Ads_Widget extends WP_Widget {
    function __construct() {
        parent::__construct(
            'bawana_ads_widget',
            'Bawana Ads Widget',
            array('description' => 'Widget iklan yang dapat dikonfigurasi melalui admin panel')
        );
    }

    public function widget($args, $instance) {
        $ads_content = get_option('bawana_ads_content', '');
        $ads_title = get_option('bawana_ads_title', 'Advertisement');

        if (empty($ads_content)) {
            return;
        }

        echo $args['before_widget'];
        if (!empty($ads_title)) {
            echo $args['before_title'] . $ads_title . $args['after_title'];
        }
        echo '<div class="bawana-ads-content">' . wp_kses_post($ads_content) . '</div>';
        echo $args['after_widget'];
    }

    public function form($instance) {
        echo '<p>Konten iklan diatur melalui <a href="' . admin_url('admin.php?page=bawana-homepage') . '">Pengaturan Beranda</a></p>';
    }

    public function update($new_instance, $old_instance) {
        return array();
    }
}

// Daftarkan widget
add_action('widgets_init', function() {
    register_widget('Bawana_Live_Streaming_Widget');
    register_widget('Bawana_Talkshow_Widget');
    register_widget('Bawana_Popular_Posts_Widget');
    register_widget('Bawana_Ads_Widget');
});

/* ----------------------------------------------------------------------------
 * Video Embed Helper Functions
 */
function bawana_get_video_embed($url, $autoplay = false, $lazy_load = true) {
    if (empty($url)) {
        return false;
    }

    $autoplay_param = $autoplay ? '&autoplay=1&mute=1' : '';
    $loading_attr = $lazy_load ? 'loading="lazy"' : '';

    // YouTube embed (including live streams)
    if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/live\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
        $video_id = $matches[1];

        // If not autoplay, show thumbnail with play button overlay
        if (!$autoplay) {
            $thumbnail_url = "https://img.youtube.com/vi/{$video_id}/maxresdefault.jpg";
            return '
            <div class="bawana-video-thumbnail" style="position: relative; cursor: pointer; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);" onclick="bawanaLoadVideo(this, \'' . $video_id . '\')">
                <img src="' . $thumbnail_url . '" alt="Video Thumbnail" style="width: 100%; height: 400px; object-fit: cover;" ' . $loading_attr . '>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,0,0,0.8); border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 0; height: 0; border-left: 25px solid white; border-top: 15px solid transparent; border-bottom: 15px solid transparent; margin-left: 5px;"></div>
                </div>
                <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px;">
                    ▶ Klik untuk memutar
                </div>
            </div>
            <script>
            function bawanaLoadVideo(element, videoId) {
                element.innerHTML = \'<iframe width="100%" height="400" src="https://www.youtube.com/embed/\' + videoId + \'?autoplay=1&rel=0&modestbranding=1" frameborder="0" allowfullscreen style="border-radius: 12px;"></iframe>\';
            }
            </script>';
        } else {
            return '<iframe width="100%" height="400" src="https://www.youtube.com/embed/' . $video_id . '?rel=0&modestbranding=1' . $autoplay_param . '" frameborder="0" allowfullscreen ' . $loading_attr . ' style="border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);"></iframe>';
        }
    }

    // Facebook embed (including live streams)
    if (strpos($url, 'facebook.com') !== false) {
        return '<div class="fb-video" data-href="' . esc_url($url) . '" data-width="auto" data-show-text="false" data-allowfullscreen="true"></div>';
    }

    // Instagram Live
    if (strpos($url, 'instagram.com') !== false) {
        return '<blockquote class="instagram-media" data-instgrm-permalink="' . esc_url($url) . '" data-instgrm-version="14"></blockquote>';
    }

    // Generic video embed for other platforms
    return '<div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);"><iframe src="' . esc_url($url) . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" frameborder="0" allowfullscreen ' . $loading_attr . '></iframe></div>';
}

function bawana_render_video_section() {
    $show_video = get_option('bawana_show_video_section', 0);
    if (!$show_video) {
        // Debug untuk admin
        if (current_user_can('manage_options')) {
            echo '<!-- VIDEO SECTION DISABLED -->';
        }
        return;
    }

    $title = get_option('bawana_video_title', 'Video Unggulan');
    $url = get_option('bawana_video_url', '');
    $description = get_option('bawana_video_description', '');
    $autoplay = get_option('bawana_video_autoplay', 0);

    if (empty($url)) {
        return;
    }

    echo '<div class="bawana-video-section" style="margin: 40px 0; padding: 30px; background: #fff; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">';

    if (!empty($title)) {
        echo '<h2 style="margin-bottom: 20px; color: #1a1a1a; font-size: 28px; font-weight: 600; text-align: center;">' . esc_html($title) . '</h2>';
    }

    // Video embed dengan lazy loading untuk SEO
    $embed_code = bawana_get_video_embed($url, $autoplay, true);
    if ($embed_code) {
        echo '<div class="bawana-video-wrapper" style="margin-bottom: 20px;">';

        if ($autoplay) {
            // Jika autoplay, delay loading untuk SEO
            echo '<div id="bawana-video-container" style="min-height: 400px; background: #f8f9fa; border-radius: 12px; display: flex; align-items: center; justify-content: center; cursor: pointer;" onclick="bawanaLoadVideoSection()">';
            echo '<div style="text-align: center;">';
            echo '<div style="width: 80px; height: 80px; background: #007cba; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">';
            echo '<span style="color: white; font-size: 30px;">▶</span>';
            echo '</div>';
            echo '<p style="color: #666; margin: 0;">Klik untuk memutar video</p>';
            echo '</div>';
            echo '</div>';

            // JavaScript untuk lazy loading
            echo '<script>';
            echo 'function bawanaLoadVideoSection() {';
            echo 'document.getElementById("bawana-video-container").innerHTML = \'' . addslashes($embed_code) . '\';';
            echo '}';

            // Auto-load setelah 3 detik jika autoplay enabled
            echo 'setTimeout(function() {';
            echo 'if (document.getElementById("bawana-video-container").innerHTML.indexOf("iframe") === -1) {';
            echo 'bawanaLoadVideoSection();';
            echo '}';
            echo '}, 3000);';
            echo '</script>';
        } else {
            // Jika tidak autoplay, langsung tampilkan
            echo $embed_code;
        }

        echo '</div>';
    }

    if (!empty($description)) {
        echo '<div class="bawana-video-description" style="text-align: center; color: #666; font-size: 16px; line-height: 1.6;">';
        echo '<p>' . nl2br(esc_html($description)) . '</p>';
        echo '</div>';
    }

    echo '</div>';
}

/* ----------------------------------------------------------------------------
 * Section Rendering Functions untuk Homepage
 */
function bawana_render_section($section_name) {
    switch ($section_name) {
        case 'hot_news':
            bawana_render_hot_news_section();
            break;
        case 'popular_articles':
            bawana_render_popular_articles_section();
            break;
        case 'featured_posts':
            bawana_render_featured_posts_section();
            break;
        case 'video_section':
            bawana_render_video_section();
            break;
        case 'latest_stories':
            bawana_render_latest_stories_section();
            break;
        default:
            // Check if it's a custom section
            $custom_sections = get_option('bawana_custom_sections', array());
            foreach ($custom_sections as $custom_section) {
                if ($custom_section['id'] === $section_name) {
                    bawana_render_custom_section($custom_section);
                    break;
                }
            }
            break;
    }
}

function bawana_render_hot_news_section() {
    if (!get_option('bawana_show_hot_news', 1)) {
        // Debug untuk admin
        if (current_user_can('manage_options')) {
            echo '<!-- HOT NEWS SECTION DISABLED -->';
        }
        return;
    }

    echo '<section class="bawana-section bawana-hot-news">';
    echo '<div class="section-header">';
    echo '<h2 class="section-title">' . esc_html(get_option('bawana_hot_news_title', 'Berita Terpanas')) . '</h2>';
    if (current_user_can('manage_options')) {
        echo '<a href="' . admin_url('admin.php?page=bawana-hot-news') . '" class="admin-edit-link">✏️ Edit Berita Terpanas</a>';
    }
    echo '</div>';
    echo '<div class="bawana-grid-5">';

    $hot_news_posts = get_option('bawana_hot_news_posts', array());

    if (!empty($hot_news_posts)) {
        $hot_query = new WP_Query(array(
            'post__in' => $hot_news_posts,
            'orderby' => 'post__in',
            'posts_per_page' => 5,
            'post_status' => 'publish'
        ));
    } else {
        $hot_query = new WP_Query(array(
            'posts_per_page' => 5,
            'ignore_sticky_posts' => 1,
            'post_status' => 'publish',
            'meta_key' => '_thumbnail_id'
        ));
    }

    if ($hot_query->have_posts()) {
        while ($hot_query->have_posts()) {
            $hot_query->the_post();

            echo '<div class="grid-item">';
            echo '<a class="item-image" href="' . get_the_permalink() . '">';
            echo bawana_get_post_thumbnail(get_the_ID(), 'medium', 'hot-news-thumb');
            echo '</a>';
            echo '<div class="item-content">';

            // Kategori
            $categories = get_the_category();
            if (!empty($categories)) {
                echo '<span class="item-category">' . esc_html($categories[0]->name) . '</span>';
            }

            // Judul
            echo '<h3 class="item-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';

            echo '</div>';
            echo '</div>';
        }
        wp_reset_postdata();
    } else {
        echo '<div class="grid-item">';
        echo '<div class="item-content">';
        echo '<p style="text-align: center; color: #666; padding: 20px;">Belum ada berita terpanas. Silakan atur melalui admin panel.</p>';
        echo '</div>';
        echo '</div>';
    }

    echo '</div>';
    echo '</section>';
}

function bawana_render_popular_articles_section() {
    if (!get_option('bawana_show_popular_articles', 1)) {
        // Debug untuk admin
        if (current_user_can('manage_options')) {
            echo '<!-- POPULAR ARTICLES SECTION DISABLED -->';
        }
        return;
    }

    echo '<section class="bawana-section bawana-top-articles">';
    echo '<div class="section-header">';
    echo '<h2 class="section-title">' . esc_html(get_option('bawana_popular_articles_title', 'Most Popular Stories')) . '</h2>';
    echo '</div>';
    echo '<div class="top-articles-grid">';

    // Query tanpa batasan featured image - gunakan fallback system
    $popular_query = new WP_Query(array(
        'posts_per_page' => 3,
        'orderby' => 'comment_count',
        'order' => 'DESC',
        'post_status' => 'publish'
    ));

    if ($popular_query->have_posts()) {
        while ($popular_query->have_posts()) {
            $popular_query->the_post();

            // Ambil kategori
            $categories = get_the_category();
            $category_name = !empty($categories) ? $categories[0]->name : 'Uncategorized';

            echo '<div class="top-article-item">';
            echo '<div class="article-image">';
            echo '<a href="' . get_the_permalink() . '">';
            echo bawana_get_post_thumbnail(get_the_ID(), 'large', 'top-article-thumb');
            echo '</a>';
            echo '</div>';
            echo '<div class="article-content">';
            echo '<div class="article-category">' . esc_html($category_name) . '</div>';
            echo '<h3 class="article-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';
            echo '<div class="article-excerpt">' . wp_trim_words(get_the_excerpt(), 20) . '</div>';
            echo '<div class="article-meta">KEEP READING</div>';
            echo '</div>';
            echo '</div>';
        }
        wp_reset_postdata();
    } else {
        echo '<div class="top-article-item">';
        echo '<div class="article-content">';
        echo '<p style="text-align: center; color: #666; padding: 40px;">Belum ada artikel populer. Artikel akan muncul setelah mendapat komentar.</p>';
        echo '</div>';
        echo '</div>';
    }

    echo '</div>';
    echo '</section>';
}

function bawana_render_featured_posts_section() {
    if (!get_option('bawana_show_featured_posts', 1)) {
        // Debug untuk admin
        if (current_user_can('manage_options')) {
            echo '<!-- FEATURED POSTS SECTION DISABLED -->';
        }
        return;
    }

    echo '<section class="bawana-section bawana-featured-posts">';
    echo '<div class="section-header">';
    echo '<h2 class="section-title">' . esc_html(get_option('bawana_featured_posts_title', 'Artikel Pilihan')) . '</h2>';
    echo '</div>';
    echo '<div class="bawana-featured-grid">';

    $featured_query = new WP_Query(array(
        'posts_per_page' => 7,
        'orderby' => 'date',
        'order' => 'DESC',
        'post_status' => 'publish'
    ));

    if ($featured_query->have_posts()) {
        // Container untuk 2 post besar
        echo '<div class="featured-main-posts">';
        $counter = 0;
        while ($featured_query->have_posts() && $counter < 2) {
            $featured_query->the_post();
            $counter++;

            echo '<article class="featured-large">';
            echo '<div class="post-thumbnail">';
            echo '<a href="' . get_the_permalink() . '">';
            echo bawana_get_post_thumbnail(get_the_ID(), 'large', 'featured-large-thumb');
            echo '</a>';
            echo '</div>';
            echo '<div class="post-content">';
            echo '<h3><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';
            echo '<p>' . wp_trim_words(get_the_excerpt(), 20) . '</p>';
            echo '<div class="post-meta">' . get_the_date() . '</div>';
            echo '</div>';
            echo '</article>';
        }
        echo '</div>';

        // Container untuk 5 post kecil
        echo '<div class="featured-side-posts">';
        while ($featured_query->have_posts()) {
            $featured_query->the_post();

            echo '<article class="featured-small">';
            echo '<div class="post-thumbnail">';
            echo '<a href="' . get_the_permalink() . '">';
            echo bawana_get_post_thumbnail(get_the_ID(), 'thumbnail', 'featured-small-thumb');
            echo '</a>';
            echo '</div>';
            echo '<div class="post-content">';
            echo '<h3><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';
            echo '<div class="post-meta">' . get_the_date() . '</div>';
            echo '</div>';
            echo '</article>';
        }
        echo '</div>';

        wp_reset_postdata();
    } else {
        echo '<div class="featured-main-posts">';
        echo '<p style="text-align: center; color: #666; padding: 40px;">Belum ada artikel pilihan.</p>';
        echo '</div>';
    }

    echo '</div>';
    echo '</section>';
}

function bawana_render_latest_stories_section() {
    if (!get_option('bawana_show_latest_stories', 1)) {
        // Debug untuk admin
        if (current_user_can('manage_options')) {
            echo '<!-- LATEST STORIES SECTION DISABLED -->';
        }
        return;
    }

    echo '<section class="bawana-section bawana-latest-stories">';
    echo '<div class="section-header">';
    echo '<h2 class="section-title">' . esc_html(get_option('bawana_latest_stories_title', 'Latest Stories')) . '</h2>';
    echo '</div>';
    echo '<div class="latest-stories-grid">';

    // Get current page for pagination
    $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
    $posts_per_page = 6; // 6 posts per halaman

    // Use transient cache for better performance (cache for 5 minutes)
    $cache_key = 'bawana_latest_stories_' . $paged;
    $latest_query = get_transient($cache_key);

    if (false === $latest_query || current_user_can('manage_options')) {
        $latest_query = new WP_Query(array(
            'posts_per_page' => $posts_per_page,
            'orderby' => 'date',
            'order' => 'DESC',
            'post_status' => 'publish',
            'paged' => $paged
        ));

        // Cache for 5 minutes (300 seconds) - good for SEO and performance
        if (!current_user_can('manage_options')) {
            set_transient($cache_key, $latest_query, 300);
        }
    }

    if ($latest_query->have_posts()) {
        while ($latest_query->have_posts()) {
            $latest_query->the_post();

            // Ambil kategori
            $categories = get_the_category();
            $category_name = !empty($categories) ? $categories[0]->name : 'Nature';

            echo '<div class="latest-story-item">';
            echo '<div class="story-image">';
            echo '<a href="' . get_the_permalink() . '">';
            echo bawana_get_post_thumbnail(get_the_ID(), 'large', 'latest-story-thumb');
            echo '</a>';
            echo '</div>';
            echo '<div class="story-content">';
            echo '<div class="story-category">' . esc_html($category_name) . '</div>';
            echo '<h3 class="story-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';

            // Add excerpt for side-by-side layout
            $excerpt = get_the_excerpt();
            if (empty($excerpt)) {
                $excerpt = wp_trim_words(get_the_content(), 15, '...');
            }
            echo '<div class="story-excerpt">' . esc_html($excerpt) . '</div>';

            echo '</div>';
            echo '</div>';
        }
        wp_reset_postdata();
    } else {
        echo '<div class="latest-story-item">';
        echo '<div class="story-content">';
        echo '<p style="text-align: center; color: #666; padding: 40px;">Belum ada berita terkini.</p>';
        echo '</div>';
        echo '</div>';
    }

    echo '</div>';

    // PERBAIKAN: Add AJAX pagination yang tidak berpindah halaman
    if ($latest_query->max_num_pages > 1) {
        echo '<div class="bawana-pagination" data-max-pages="' . $latest_query->max_num_pages . '" data-current-page="' . $paged . '">';

        // Previous button
        if ($paged > 1) {
            echo '<a href="#" class="page-numbers prev ajax-page-link" data-page="' . ($paged - 1) . '">‹</a>';
        }

        // Page numbers
        for ($i = 1; $i <= $latest_query->max_num_pages; $i++) {
            if ($i == $paged) {
                echo '<span class="page-numbers current">' . $i . '</span>';
            } else {
                echo '<a href="#" class="page-numbers ajax-page-link" data-page="' . $i . '">' . $i . '</a>';
            }
        }

        // Next button
        if ($paged < $latest_query->max_num_pages) {
            echo '<a href="#" class="page-numbers next ajax-page-link" data-page="' . ($paged + 1) . '">›</a>';
        }

        echo '</div>';

        // Loading indicator
        echo '<div class="latest-stories-loading" style="display: none; text-align: center; padding: 20px;">';
        echo '<div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #d4af37; border-radius: 50%; animation: spin 1s linear infinite;"></div>';
        echo '<p style="margin-top: 10px; color: #666;">Memuat berita...</p>';
        echo '</div>';
    }

    echo '</section>';
}

function bawana_render_custom_section($section) {
    echo '<section class="bawana-section bawana-custom-section">';
    echo '<div class="section-header">';
    echo '<h2 class="section-title">' . esc_html($section['title']) . '</h2>';
    if (current_user_can('manage_options')) {
        echo '<a href="' . admin_url('admin.php?page=bawana-homepage') . '" class="admin-edit-link">⚙️ Pengaturan</a>';
    }
    echo '</div>';

    // Build query based on section type
    $query_args = array(
        'posts_per_page' => $section['count'],
        'post_status' => 'publish',
        'ignore_sticky_posts' => 1
    );

    switch ($section['type']) {
        case 'category':
            if (!empty($section['category'])) {
                $query_args['cat'] = $section['category'];
            }
            $query_args['orderby'] = 'date';
            $query_args['order'] = 'DESC';
            break;
        case 'popular':
            $query_args['meta_key'] = 'post_views_count';
            $query_args['orderby'] = 'meta_value_num';
            $query_args['order'] = 'DESC';
            break;
        case 'latest':
        default:
            $query_args['orderby'] = 'date';
            $query_args['order'] = 'DESC';
            break;
    }

    $custom_query = new WP_Query($query_args);

    // Render based on grid type
    $grid_class = 'bawana-' . $section['grid'];
    echo '<div class="' . $grid_class . '">';

    if ($custom_query->have_posts()) {
        while ($custom_query->have_posts()) {
            $custom_query->the_post();

            echo '<div class="grid-item">';
            if (has_post_thumbnail()) {
                echo '<div class="item-image">';
                echo '<a href="' . get_the_permalink() . '">';
                the_post_thumbnail('medium');
                echo '</a></div>';
            }
            echo '<div class="item-content">';
            $categories = get_the_category();
            if (!empty($categories)) {
                echo '<span class="item-category">' . esc_html($categories[0]->name) . '</span>';
            }
            echo '<h3 class="item-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';
            echo '<div class="item-meta">';
            echo '<span class="item-date">' . get_the_date() . '</span>';
            echo '</div>';
            echo '</div></div>';
        }
    } else {
        echo '<p>Belum ada konten untuk section ini.</p>';
    }

    wp_reset_postdata();
    echo '</div></section>';
}

/* ----------------------------------------------------------------------------
 * CSS untuk Custom Sections
 * ---------------------------------------------------------------------------- */
add_action('wp_head', 'bawana_custom_sections_css');
function bawana_custom_sections_css() {
    echo '<style>
    .bawana-custom-section {
        margin-bottom: 40px;
    }

    .bawana-grid-3 {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .bawana-grid-4 {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .bawana-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .bawana-list .grid-item {
        display: flex;
        gap: 15px;
        align-items: flex-start;
    }

    .bawana-list .item-image {
        flex-shrink: 0;
        width: 150px;
    }

    .bawana-list .item-content {
        flex: 1;
    }

    .grid-item {
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .grid-item:hover {
        transform: translateY(-5px);
    }

    .item-image img {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    /* CSS untuk Hot News - Frame 16:9 seragam */
    .bawana-hot-news {
        background: #fff;
        padding: 30px;
        margin: 30px 0;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }

    .bawana-hot-news .section-title {
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
        border-bottom: 3px solid #d4af37;
        padding-bottom: 15px;
    }

    .bawana-hot-news .bawana-grid-5 {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 20px;
    }

    .bawana-hot-news .grid-item {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .bawana-hot-news .grid-item:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .bawana-hot-news .item-image {
        position: relative;
        width: 100%;
        aspect-ratio: 16/9;
        overflow: hidden;
        display: block;
    }

    .bawana-hot-news .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .bawana-hot-news .grid-item:hover .item-image img {
        transform: scale(1.05);
    }

    .bawana-hot-news .item-content {
        padding: 15px;
    }

    .bawana-hot-news .item-category {
        background: #d4af37;
        color: #fff;
        font-size: 11px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 4px;
        text-transform: uppercase;
        margin-bottom: 8px;
        display: inline-block;
    }

    .bawana-hot-news .item-title {
        font-size: 14px;
        font-weight: 600;
        line-height: 1.4;
        margin: 0;
        color: #1a1a1a;
    }

    .bawana-hot-news .item-title a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .bawana-hot-news .item-title a:hover {
        color: #d4af37;
    }

    /* Responsive untuk Hot News */
    @media (max-width: 1200px) {
        .bawana-hot-news .bawana-grid-5 {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 768px) {
        .bawana-hot-news .bawana-grid-5 {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .bawana-hot-news .item-content {
            padding: 12px;
        }
        .bawana-hot-news .item-title {
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .bawana-hot-news .bawana-grid-5 {
            grid-template-columns: 1fr;
        }
    }

    /* CSS khusus untuk Popular Articles dan Latest Stories */
    .bawana-popular-articles,
    .bawana-latest-stories {
        background: #fff;
        padding: 30px;
        margin: 30px 0;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }

    .bawana-popular-articles .section-title,
    .bawana-latest-stories .section-title {
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
        border-bottom: 3px solid #d4af37;
        padding-bottom: 15px;
    }

    .bawana-list-3 .list-item {
        display: flex;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
        align-items: flex-start;
    }

    .bawana-list-3 .list-item:last-child {
        border-bottom: none;
    }

    .bawana-list-3 .item-image {
        flex-shrink: 0;
        width: 200px;
        height: 120px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
    }

    .bawana-list-3 .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .bawana-list-3 .list-item:hover .item-image img {
        transform: scale(1.05);
    }

    .bawana-list-3 .item-content {
        flex: 1;
    }

    .bawana-list-3 .item-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .bawana-list-3 .item-title a {
        color: #1a1a1a;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .bawana-list-3 .item-title a:hover {
        color: #d4af37;
    }

    .bawana-list-3 .item-meta {
        font-size: 12px;
        color: #666;
        margin-bottom: 5px;
    }

    .bawana-list-3 .item-excerpt {
        font-size: 14px;
        color: #555;
        line-height: 1.5;
    }

    /* CSS untuk Featured Posts - Layout 2 besar + 5 kecil */
    .bawana-featured-posts {
        background: #fff;
        padding: 30px;
        margin: 30px 0;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }

    .bawana-featured-posts .section-title {
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
        border-bottom: 3px solid #d4af37;
        padding-bottom: 15px;
    }

    .bawana-featured-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
    }

    .featured-main-posts {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .featured-side-posts {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .featured-large {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .featured-large:hover {
        transform: translateY(-5px);
    }

    .featured-large .post-thumbnail {
        position: relative;
        aspect-ratio: 16/9;
        overflow: hidden;
    }

    .featured-large .post-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .featured-large:hover .post-thumbnail img {
        transform: scale(1.05);
    }

    .featured-large .post-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.8));
        color: white;
        padding: 20px;
    }

    .featured-large .post-content h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        line-height: 1.3;
    }

    .featured-large .post-content h3 a {
        color: white;
        text-decoration: none;
    }

    .featured-large .post-content p {
        font-size: 14px;
        margin: 0 0 8px 0;
        opacity: 0.9;
    }

    .featured-large .post-meta {
        font-size: 12px;
        opacity: 0.8;
    }

    .featured-small {
        display: flex;
        gap: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        transition: background-color 0.3s ease;
    }

    .featured-small:hover {
        background: #e9ecef;
    }

    .featured-small .post-thumbnail {
        flex-shrink: 0;
        width: 80px;
        height: 60px;
        border-radius: 6px;
        overflow: hidden;
    }

    .featured-small .post-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .featured-small .post-content {
        flex: 1;
    }

    .featured-small .post-content h3 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        line-height: 1.3;
    }

    .featured-small .post-content h3 a {
        color: #1a1a1a;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .featured-small .post-content h3 a:hover {
        color: #d4af37;
    }

    .featured-small .post-meta {
        font-size: 11px;
        color: #666;
    }

    /* Responsive untuk Featured Posts */
    @media (max-width: 768px) {
        .bawana-featured-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .featured-main-posts {
            grid-template-columns: 1fr;
        }
    }

    /* CSS untuk Top Articles (Most Popular Stories) */
    .bawana-top-articles {
        background: #fff;
        padding: 30px;
        margin: 30px 0;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }

    .bawana-top-articles .section-title {
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
        border-bottom: 3px solid #d4af37;
        padding-bottom: 15px;
        font-style: italic;
    }

    .top-articles-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
    }

    .top-article-item {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .top-article-item:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .top-article-item .article-image {
        position: relative;
        aspect-ratio: 16/9;
        overflow: hidden;
    }

    .top-article-item .article-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .top-article-item:hover .article-image img {
        transform: scale(1.05);
    }

    .top-article-item .article-content {
        padding: 20px;
        text-align: center;
    }

    .top-article-item .article-category {
        color: #d4af37;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 10px;
        font-style: italic;
    }

    .top-article-item .article-title {
        font-size: 18px;
        font-weight: 600;
        line-height: 1.3;
        margin: 0 0 12px 0;
        color: #1a1a1a;
        text-transform: uppercase;
    }

    .top-article-item .article-title a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .top-article-item .article-title a:hover {
        color: #d4af37;
    }

    .top-article-item .article-excerpt {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 15px;
    }

    .top-article-item .article-meta {
        color: #d4af37;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .top-article-item .article-meta:hover {
        color: #1a1a1a;
    }

    /* Responsive untuk Top Articles */
    @media (max-width: 1024px) {
        .top-articles-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .top-articles-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .top-article-item .article-content {
            padding: 15px;
        }

        .top-article-item .article-title {
            font-size: 16px;
        }
    }

    /* CSS untuk Latest Stories */
    .bawana-latest-stories {
        background: #fff;
        padding: 30px;
        margin: 30px 0;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }

    .bawana-latest-stories .section-title {
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
        border-bottom: 3px solid #d4af37;
        padding-bottom: 15px;
        font-style: italic;
    }

    .latest-stories-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .latest-story-item {
        display: flex;
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        min-height: 140px;
    }

    .latest-story-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    /* GAMBAR DI SEBELAH KIRI */
    .latest-story-item .story-image {
        flex: 0 0 40%;
        position: relative;
        overflow: hidden;
    }

    .latest-story-item .story-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .latest-story-item:hover .story-image img {
        transform: scale(1.05);
    }

    /* TEKS DI SEBELAH KANAN - TERPISAH DARI GAMBAR */
    .latest-story-item .story-content {
        flex: 1;
        padding: 15px 20px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        background: #ffffff;
        color: #333;
    }

    .latest-story-item .story-category {
        background: #d4af37;
        color: white;
        padding: 4px 8px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 8px;
        font-style: italic;
        display: inline-block;
        border-radius: 2px;
        align-self: flex-start;
    }

    .latest-story-item .story-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.3;
        margin: 0 0 8px 0;
        color: #333;
    }

    .latest-story-item .story-title a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .latest-story-item .story-title a:hover {
        color: #d4af37;
    }

    .latest-story-item .story-excerpt {
        font-size: 13px;
        line-height: 1.4;
        margin: 0;
        color: #666;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        flex-grow: 1;
    }

    .latest-story-item .story-meta {
        color: #d4af37;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        cursor: pointer;
        transition: color 0.3s ease;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .latest-story-item .story-meta:hover {
        color: white;
    }

    /* Responsive untuk Latest Stories */
    @media (max-width: 1024px) {
        .latest-stories-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .latest-story-item .story-image {
            flex: 0 0 35%;
        }
    }

    @media (max-width: 768px) {
        .latest-story-item {
            flex-direction: column;
            min-height: auto;
        }

        .latest-story-item .story-image {
            flex: none;
            height: 200px;
        }

        .latest-story-item .story-content {
            padding: 15px;
        }

        .latest-story-item .story-title {
            font-size: 14px;
        }
    }

    /* Pagination Styling */
    .bawana-pagination {
        text-align: center;
        margin: 30px 0;
        padding: 20px 0;
    }

    .bawana-pagination .page-numbers {
        display: inline-block;
        padding: 8px 12px;
        margin: 0 4px;
        background: #f8f9fa;
        color: #333;
        text-decoration: none;
        border-radius: 4px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .bawana-pagination .page-numbers:hover {
        background: #d4af37;
        color: white;
        border-color: #d4af37;
        transform: translateY(-1px);
    }

    .bawana-pagination .page-numbers.current {
        background: #1a1a1a;
        color: white;
        border-color: #1a1a1a;
    }

    .bawana-pagination .page-numbers.prev,
    .bawana-pagination .page-numbers.next {
        font-size: 18px;
        padding: 8px 15px;
    }

    .bawana-pagination .page-numbers.dots {
        background: transparent;
        border: none;
        color: #666;
        cursor: default;
    }

    .bawana-pagination .page-numbers.dots:hover {
        background: transparent;
        color: #666;
        transform: none;
    }

    @media (max-width: 768px) {
        .bawana-pagination .page-numbers {
            padding: 6px 10px;
            margin: 0 2px;
            font-size: 14px;
        }
    }

    .bawana-list .item-image img {
        height: 100px;
    }

    .item-content {
        padding: 15px;
    }

    .item-category {
        background: #007cba;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        text-transform: uppercase;
        font-weight: bold;
    }

    .item-title {
        margin: 10px 0;
        font-size: 16px;
        line-height: 1.4;
    }

    .item-title a {
        color: #333;
        text-decoration: none;
    }

    .item-title a:hover {
        color: #007cba;
    }

    .item-meta {
        color: #666;
        font-size: 14px;
    }

    @media (max-width: 768px) {
        .bawana-grid-3,
        .bawana-grid-4 {
            grid-template-columns: 1fr;
        }

        .bawana-list .grid-item {
            flex-direction: column;
        }

        .bawana-list .item-image {
            width: 100%;
        }
    }
    </style>';
}

/* ----------------------------------------------------------------------------
 * Facebook SDK untuk Video Embed
 */
add_action('wp_footer', 'bawana_add_facebook_sdk');
function bawana_add_facebook_sdk() {
    // Hanya load jika ada video Facebook di halaman
    $video_url = get_option('bawana_video_url', '');
    $live_url = get_option('bawana_live_streaming_url', '');

    if (strpos($video_url, 'facebook.com') !== false || strpos($live_url, 'facebook.com') !== false) {
        echo '<div id="fb-root"></div>';
        echo '<script async defer crossorigin="anonymous" src="https://connect.facebook.net/id_ID/sdk.js#xfbml=1&version=v18.0"></script>';
    }
}

/* ----------------------------------------------------------------------------
 * Setup Sidebar Otomatis untuk Bawana News
 */
function bawana_news_setup_sidebar() {
    // Get widget order from admin settings
    $widget_order = get_option('bawana_widget_order', 'live,popular,talkshow');
    $show_sidebar_widgets = get_option('bawana_show_sidebar_widgets', 1);

    // If sidebar widgets are disabled, clear sidebar
    if (!$show_sidebar_widgets) {
        $sidebars_widgets = get_option('sidebars_widgets', array());
        $sidebars_widgets['td-default'] = array();
        update_option('sidebars_widgets', $sidebars_widgets);
        return;
    }

    // Hapus widget yang ada di sidebar default
    $sidebars_widgets = get_option('sidebars_widgets', array());
    $sidebars_widgets['td-default'] = array();

    // Setup widget baru sesuai urutan yang dipilih admin
    $widget_instances = array();
    $order_array = explode(',', $widget_order);

    $widget_counter = 1;
    foreach ($order_array as $widget_type) {
        $widget_type = trim($widget_type);

        switch ($widget_type) {
            case 'live':
                $live_widget_id = 'bawana_live_streaming-' . $widget_counter;
                $widget_instances['bawana_live_streaming'][$widget_counter] = array();
                $sidebars_widgets['td-default'][] = $live_widget_id;
                break;

            case 'popular':
                $popular_widget_id = 'bawana_popular_posts-' . $widget_counter;
                $widget_instances['bawana_popular_posts'][$widget_counter] = array(
                    'title' => 'BERITA POPULER',
                    'number' => 5
                );
                $sidebars_widgets['td-default'][] = $popular_widget_id;
                break;

            case 'talkshow':
                $talkshow_widget_id = 'bawana_talkshow-' . $widget_counter;
                $widget_instances['bawana_talkshow'][$widget_counter] = array();
                $sidebars_widgets['td-default'][] = $talkshow_widget_id;
                break;
        }
        $widget_counter++;
    }

    // Add ads widget if enabled
    $show_ads_section = get_option('bawana_show_ads_section', 0);
    if ($show_ads_section) {
        $ads_widget_id = 'bawana_ads_widget-1';
        $widget_instances['bawana_ads_widget'][1] = array();
        $sidebars_widgets['td-default'][] = $ads_widget_id;
    }

    // Simpan konfigurasi widget
    foreach ($widget_instances as $widget_type => $instances) {
        update_option('widget_' . $widget_type, $instances);
    }

    // Simpan konfigurasi sidebar
    update_option('sidebars_widgets', $sidebars_widgets);

    // Tandai bahwa sidebar setup sudah selesai
    update_option('bawana_sidebar_setup_done', true);
}

// Jalankan setup sidebar setelah theme diaktifkan
add_action('after_switch_theme', 'bawana_news_setup_sidebar');

// Juga jalankan saat init jika belum di-setup
add_action('init', function() {
    if (!get_option('bawana_sidebar_setup_done', false)) {
        bawana_news_setup_sidebar();
    }
});

// Fungsi untuk debug widget (hanya untuk admin)
add_action('wp_footer', 'bawana_debug_widgets');
function bawana_debug_widgets() {
    if (current_user_can('manage_options') && isset($_GET['debug_widgets'])) {
        echo '<div style="position: fixed; bottom: 10px; right: 10px; background: #fff; padding: 15px; border: 2px solid #333; z-index: 9999; max-width: 300px; font-size: 12px;">';
        echo '<h4>Debug Widget Bawana News</h4>';

        $sidebars_widgets = get_option('sidebars_widgets', array());
        echo '<strong>Widgets di td-default:</strong><br>';
        if (isset($sidebars_widgets['td-default'])) {
            foreach ($sidebars_widgets['td-default'] as $widget) {
                echo '- ' . $widget . '<br>';
            }
        } else {
            echo 'Sidebar td-default tidak ditemukan<br>';
        }

        echo '<br><strong>Widget Options:</strong><br>';
        echo 'Live Streaming: ' . (get_option('bawana_live_streaming_content') ? 'Ada konten' : 'Kosong') . '<br>';
        echo 'Talkshow: ' . (get_option('bawana_talkshow_content') ? 'Ada konten' : 'Kosong') . '<br>';

        echo '<br><a href="' . remove_query_arg('debug_widgets') . '">Tutup Debug</a>';
        echo '</div>';
    }
}

// Force refresh widget setup jika diperlukan
add_action('init', function() {
    // PERBAIKAN: Reset widgets dengan nonce validation
    if (current_user_can('manage_options') && isset($_GET['reset_bawana_widgets'])) {
        // Validasi nonce untuk keamanan
        if (isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'reset_bawana_widgets')) {
            delete_option('bawana_sidebar_setup_done');
            bawana_news_setup_sidebar();
            wp_safe_redirect(remove_query_arg(array('reset_bawana_widgets', '_wpnonce')));
            exit;
        }
    }
});

/* ----------------------------------------------------------------------------
 * Kontrol Sidebar yang Fleksibel untuk Bawana News
 */

// Tambahkan meta box untuk kontrol sidebar di post/page editor
add_action('add_meta_boxes', 'bawana_news_add_sidebar_meta_box');
function bawana_news_add_sidebar_meta_box() {
    // Hanya tampilkan untuk Admin, sembunyikan dari Editor untuk kesederhanaan
    if (current_user_can('manage_options')) {
        add_meta_box(
            'bawana_sidebar_control',
            '📐 Layout Sidebar',
            'bawana_news_sidebar_meta_box_callback',
            array('post', 'page'),
            'side',
            'default'
        );
    }
}

// Callback untuk meta box sidebar control
function bawana_news_sidebar_meta_box_callback($post) {
    wp_nonce_field('bawana_sidebar_meta_box', 'bawana_sidebar_meta_box_nonce');

    $sidebar_setting = get_post_meta($post->ID, '_bawana_sidebar_setting', true);
    if (empty($sidebar_setting)) {
        $sidebar_setting = 'default'; // Default mengikuti setting global
    }
    ?>
    <p>
        <label for="bawana_sidebar_setting"><strong>Pengaturan Sidebar:</strong></label><br>
        <select name="bawana_sidebar_setting" id="bawana_sidebar_setting" style="width: 100%; margin-top: 5px;">
            <option value="default" <?php selected($sidebar_setting, 'default'); ?>>Default (Ikuti Setting Global)</option>
            <option value="with_sidebar" <?php selected($sidebar_setting, 'with_sidebar'); ?>>Tampilkan Sidebar</option>
            <option value="no_sidebar" <?php selected($sidebar_setting, 'no_sidebar'); ?>>Tanpa Sidebar (Full Width)</option>
        </select>
    </p>
    <p style="font-size: 12px; color: #666; margin-top: 10px;">
        <strong>Default:</strong> Halaman mengikuti pengaturan global tema.<br>
        <strong>Dengan Sidebar:</strong> Paksa tampilkan sidebar di halaman ini.<br>
        <strong>Tanpa Sidebar:</strong> Halaman full width tanpa sidebar.
    </p>
    <?php
}

// Simpan setting sidebar saat post disimpan
add_action('save_post', 'bawana_news_save_sidebar_meta_box');
function bawana_news_save_sidebar_meta_box($post_id) {
    // Verifikasi nonce
    if (!isset($_POST['bawana_sidebar_meta_box_nonce']) ||
        !wp_verify_nonce($_POST['bawana_sidebar_meta_box_nonce'], 'bawana_sidebar_meta_box')) {
        return;
    }

    // Cek autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Cek permission
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Simpan setting
    if (isset($_POST['bawana_sidebar_setting'])) {
        $sidebar_setting = sanitize_text_field($_POST['bawana_sidebar_setting']);
        update_post_meta($post_id, '_bawana_sidebar_setting', $sidebar_setting);
    }
}

// Batasi akses Editor dari Appearance > Widgets
add_action('admin_menu', 'bawana_news_restrict_widgets_access', 999);
function bawana_news_restrict_widgets_access() {
    // Hapus akses widgets untuk non-admin
    if (!current_user_can('manage_options')) {
        remove_submenu_page('themes.php', 'widgets.php');
    }
}

/* ----------------------------------------------------------------------------
 * Optimasi Responsif dan Mobile-First untuk Bawana News
 */

// Tambahkan viewport meta tag untuk responsive design
add_action('wp_head', 'bawana_news_add_viewport_meta', 1);
function bawana_news_add_viewport_meta() {
    echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">' . "\n";
}

// Deteksi mobile device dan adjust widget display
add_action('wp', 'bawana_news_mobile_optimizations');
function bawana_news_mobile_optimizations() {
    if (wp_is_mobile()) {
        // Tambahkan class khusus untuk mobile
        add_filter('body_class', function($classes) {
            $classes[] = 'bawana-mobile-device';
            return $classes;
        });

        // Prioritaskan widget penting di mobile
        add_filter('dynamic_sidebar_params', 'bawana_news_mobile_widget_priority');
    }
}

// Atur prioritas widget untuk mobile
function bawana_news_mobile_widget_priority($params) {
    if (wp_is_mobile() && isset($params[0]['widget_name'])) {
        $widget_name = $params[0]['widget_name'];

        // Sembunyikan widget talkshow di mobile untuk menghemat ruang
        if (strpos($widget_name, 'bawana_talkshow') !== false) {
            $params[0]['before_widget'] = '<div style="display: none;">';
            $params[0]['after_widget'] = '</div>';
        }
    }

    return $params;
}

// Tambahkan lazy loading untuk iframe video
add_filter('the_content', 'bawana_news_add_lazy_loading_to_iframes');
function bawana_news_add_lazy_loading_to_iframes($content) {
    // Tambahkan loading="lazy" ke semua iframe
    $content = preg_replace('/<iframe(.*?)>/i', '<iframe$1 loading="lazy">', $content);
    return $content;
}

// Optimasi gambar untuk mobile
add_filter('wp_get_attachment_image_attributes', 'bawana_news_responsive_image_attributes', 10, 3);
function bawana_news_responsive_image_attributes($attr, $attachment, $size) {
    // Tambahkan loading lazy untuk gambar
    $attr['loading'] = 'lazy';

    // Tambahkan responsive attributes
    if (!isset($attr['sizes'])) {
        $attr['sizes'] = '(max-width: 480px) 100vw, (max-width: 768px) 50vw, 33vw';
    }

    return $attr;
}

// Tambahkan preconnect untuk performance
add_action('wp_head', 'bawana_news_add_preconnect_hints', 2);
function bawana_news_add_preconnect_hints() {
    echo '<link rel="preconnect" href="https://www.youtube.com">' . "\n";
    echo '<link rel="preconnect" href="https://www.facebook.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
}

// Tambahkan critical CSS inline untuk above-the-fold content
add_action('wp_head', 'bawana_news_critical_css', 3);
function bawana_news_critical_css() {
    if (is_front_page() || is_home()) {
        ?>
        <style id="bawana-critical-css">
        .td-header-wrap { background: var(--bawana-gradient-primary, #1e3a8a); }
        .td-logo-text { color: var(--bawana-primary-blue, #1e3a8a) !important; }
        .td-main-content-wrap { min-height: 50vh; }
        .bawana-video-container { aspect-ratio: 16/9; }
        @media (max-width: 480px) {
            .widget.bawana_talkshow { display: none; }
            .bawana-video-container { aspect-ratio: 4/3; }
        }
        </style>
        <?php
    }
}

// Enqueue JavaScript untuk optimasi mobile
add_action('wp_enqueue_scripts', 'bawana_news_enqueue_mobile_scripts');
function bawana_news_enqueue_mobile_scripts() {
    // Enqueue mobile optimizations script
    wp_enqueue_script(
        'bawana-mobile-optimizations',
        get_stylesheet_directory_uri() . '/js/bawana-mobile-optimizations.js',
        array('jquery'),
        '1.0.0',
        true
    );

    // Localize script dengan data yang diperlukan
    wp_localize_script('bawana-mobile-optimizations', 'bawanaAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('bawana_ajax_nonce'),
        'isMobile' => wp_is_mobile() ? 'true' : 'false'
    ));
}

// Tambahkan body classes untuk styling kondisional
add_filter('body_class', 'bawana_news_body_classes');
function bawana_news_body_classes($classes) {
    // Tambahkan class berdasarkan device type
    if (wp_is_mobile()) {
        $classes[] = 'bawana-mobile';
    } else {
        $classes[] = 'bawana-desktop';
    }

    // Tambahkan class berdasarkan sidebar setting
    if (is_singular()) {
        $sidebar_setting = get_post_meta(get_the_ID(), '_bawana_sidebar_setting', true);
        if ($sidebar_setting) {
            $classes[] = 'bawana-sidebar-' . $sidebar_setting;
        }
    }

    return $classes;
}

// Optimasi database queries untuk performance
add_action('init', 'bawana_news_optimize_queries');
function bawana_news_optimize_queries() {
    // Disable emoji scripts untuk performance
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');

    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');

    // Optimize heartbeat
    add_filter('heartbeat_settings', function($settings) {
        $settings['interval'] = 60; // 60 seconds
        return $settings;
    });
}

/* ----------------------------------------------------------------------------
 * Sederhanakan Interface Editor untuk Operator Non-Teknis
 */

// Sembunyikan meta box yang tidak perlu di halaman edit post
add_action('admin_menu', 'bawana_news_remove_meta_boxes');
function bawana_news_remove_meta_boxes() {
    // Hanya untuk role editor dan author
    if (!current_user_can('manage_options')) {
        remove_meta_box('postcustom', 'post', 'normal'); // Custom Fields
        remove_meta_box('postexcerpt', 'post', 'normal'); // Excerpt
        remove_meta_box('trackbacksdiv', 'post', 'normal'); // Trackbacks
        remove_meta_box('commentstatusdiv', 'post', 'normal'); // Discussion
        remove_meta_box('slugdiv', 'post', 'normal'); // Slug
        remove_meta_box('authordiv', 'post', 'normal'); // Author
    }
}

// Sederhanakan admin bar
add_action('wp_before_admin_bar_render', 'bawana_news_simplify_admin_bar');
function bawana_news_simplify_admin_bar() {
    global $wp_admin_bar;

    if (!current_user_can('manage_options')) {
        $wp_admin_bar->remove_menu('wp-logo');
        $wp_admin_bar->remove_menu('about');
        $wp_admin_bar->remove_menu('wporg');
        $wp_admin_bar->remove_menu('documentation');
        $wp_admin_bar->remove_menu('support-forums');
        $wp_admin_bar->remove_menu('feedback');
        $wp_admin_bar->remove_menu('themes');
        $wp_admin_bar->remove_menu('widgets');
        $wp_admin_bar->remove_menu('menus');
    }
}

// Sederhanakan menu admin untuk operator
add_action('admin_menu', 'bawana_news_simplify_admin_menu', 999);
function bawana_news_simplify_admin_menu() {
    if (!current_user_can('manage_options')) {
        // Sembunyikan menu yang tidak perlu
        remove_menu_page('tools.php');
        remove_menu_page('options-general.php');
        remove_menu_page('themes.php');
        remove_menu_page('plugins.php');
        remove_menu_page('users.php');
        remove_menu_page('edit.php?post_type=page');

        // Sembunyikan submenu yang tidak perlu
        remove_submenu_page('edit.php', 'edit-tags.php?taxonomy=post_tag');
    }
}

// Tambahkan CSS untuk menyederhanakan interface
add_action('admin_head', 'bawana_news_admin_css');
function bawana_news_admin_css() {
    if (!current_user_can('manage_options')) {
        ?>
        <style>
        /* Sembunyikan elemen yang membingungkan */
        #screen-meta-links,
        #contextual-help-link-wrap,
        #screen-options-link-wrap,
        .misc-pub-section:not(.misc-pub-post-status):not(.misc-pub-visibility):not(.misc-pub-curtime),
        #minor-publishing-actions,
        .rwmb-meta-box,
        #tagsdiv-post_tag,
        #postimagediv .inside p:not(.hide-if-no-js) {
            display: none !important;
        }

        /* Perbesar area editor */
        #postdivrich {
            margin-top: 20px;
        }

        /* Styling untuk area yang penting */
        #titlediv {
            margin-bottom: 20px;
        }

        #titlediv #title {
            font-size: 18px;
            padding: 12px;
            border: 2px solid #1e3a8a;
            border-radius: 4px;
        }

        #categorydiv {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #1e3a8a;
        }

        #postimagediv {
            background: #fff7ed;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #d4af37;
        }

        /* Tambahkan label yang jelas */
        #titlediv:before {
            content: "📝 JUDUL BERITA";
            display: block;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 8px;
            font-size: 14px;
        }

        #categorydiv h2:before {
            content: "📂 ";
        }

        #postimagediv h2:before {
            content: "🖼️ ";
        }

        /* Perbesar tombol publish */
        #publish {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6) !important;
            border: none !important;
            padding: 12px 24px !important;
            font-size: 16px !important;
            font-weight: bold !important;
            border-radius: 6px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        #publish:hover {
            background: linear-gradient(135deg, #1e40af, #2563eb) !important;
        }
        </style>
        <?php
    }
}

// ============================================================================
// BAWANA LOGO INTEGRATION & HEADER CUSTOMIZATION
// ============================================================================

// REMOVED - Duplicate logo support removed to prevent conflicts
// Using bawana_news_setup_theme instead

// HAPUS ANIMASI LOGO - HANYA GUNAKAN LOGO STATIS

// Add admin notice for logo setup
add_action('admin_notices', 'bawana_logo_admin_notice');
function bawana_logo_admin_notice() {
    $custom_logo_id = get_theme_mod('custom_logo');

    if (!$custom_logo_id && current_user_can('manage_options')) {
        ?>
        <div class="notice notice-info is-dismissible">
            <p><strong>Bawana News:</strong> Silakan upload logo di <a href="<?php echo admin_url('customize.php'); ?>">Customizer > Site Identity</a> untuk tampilan yang optimal.</p>
        </div>
        <?php
    }
}

// ============================================================================
// BAWANA NEWS - SOCIAL SHARE FUNCTIONALITY
// ============================================================================

// Enqueue Font Awesome untuk ikon sosial media
add_action('wp_enqueue_scripts', 'bn_enqueue_social_share_assets');
function bn_enqueue_social_share_assets() {
    // Font Awesome untuk ikon
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');

    // JavaScript untuk copy link functionality
    wp_enqueue_script('bawana-social-share', get_stylesheet_directory_uri() . '/js/social-share.js', array('jquery'), '1.0.0', true);

    // Localize script untuk teks
    wp_localize_script('bawana-social-share', 'bawanaShare', array(
        'copied_text' => 'Link disalin!',
        'copy_error' => 'Gagal menyalin link'
    ));
}

// ============================================================================
// BAWANA NEWS - DISABLE COMMENTS COMPLETELY
// ============================================================================

// Nonaktifkan komentar untuk semua post types
add_action('admin_init', 'bn_disable_comments_admin');
function bn_disable_comments_admin() {
    // Hapus dukungan komentar dari semua post types
    $post_types = get_post_types();
    foreach ($post_types as $post_type) {
        if (post_type_supports($post_type, 'comments')) {
            remove_post_type_support($post_type, 'comments');
            remove_post_type_support($post_type, 'trackbacks');
        }
    }
}

// Tutup komentar untuk semua post yang ada
add_action('init', 'bn_disable_existing_comments');
function bn_disable_existing_comments() {
    // Update semua post untuk menutup komentar
    global $wpdb;
    $wpdb->query("UPDATE {$wpdb->posts} SET comment_status = 'closed', ping_status = 'closed'");
}

// Hapus menu komentar dari admin
add_action('admin_menu', 'bn_remove_comments_menu');
function bn_remove_comments_menu() {
    remove_menu_page('edit-comments.php');
}

// PERBAIKAN: Redirect halaman komentar admin dengan validasi yang lebih aman
add_action('admin_init', 'bn_redirect_comments_page');
function bn_redirect_comments_page() {
    global $pagenow;
    // Hanya redirect jika benar-benar di halaman comments dan user memiliki akses
    if ($pagenow === 'edit-comments.php' && current_user_can('moderate_comments')) {
        wp_safe_redirect(admin_url());
        exit;
    }
}

// Hapus widget komentar dari dashboard
add_action('wp_dashboard_setup', 'bn_remove_comments_dashboard_widget');
function bn_remove_comments_dashboard_widget() {
    remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
}

// Hapus komentar dari admin bar
add_action('wp_before_admin_bar_render', 'bn_remove_comments_admin_bar');
function bn_remove_comments_admin_bar() {
    global $wp_admin_bar;
    $wp_admin_bar->remove_menu('comments');
}

// Filter untuk mengembalikan array kosong untuk komentar
add_filter('comments_array', '__return_empty_array', 10, 2);

// Nonaktifkan feed komentar
add_action('init', 'bn_disable_comment_feeds');
function bn_disable_comment_feeds() {
    remove_action('wp_head', 'feed_links_extra', 3);
    add_filter('feed_links_show_comments_feed', '__return_false');
}

// Hapus kolom komentar dari post list di admin
add_filter('manage_posts_columns', 'bn_remove_comments_column');
add_filter('manage_pages_columns', 'bn_remove_comments_column');
function bn_remove_comments_column($columns) {
    unset($columns['comments']);
    return $columns;
}

// Hapus quick edit untuk komentar
add_action('admin_print_scripts-edit.php', 'bn_remove_comments_quick_edit');
function bn_remove_comments_quick_edit() {
    echo '<style>
        .inline-edit-row fieldset.inline-edit-col-right .inline-edit-group:last-child {
            display: none !important;
        }
    </style>';
}

// Hapus meta box komentar dari post editor
add_action('admin_init', 'bn_remove_comments_meta_box');
function bn_remove_comments_meta_box() {
    remove_meta_box('commentsdiv', 'post', 'normal');
    remove_meta_box('commentsdiv', 'page', 'normal');
    remove_meta_box('commentstatusdiv', 'post', 'normal');
    remove_meta_box('commentstatusdiv', 'page', 'normal');
    remove_meta_box('trackbacksdiv', 'post', 'normal');
    remove_meta_box('trackbacksdiv', 'page', 'normal');
}

/* ----------------------------------------------------------------------------
 * FINAL SYSTEM CHECK - MEMASTIKAN SEMUA SETTING BERFUNGSI
 */
add_action('wp_footer', 'bawana_final_system_check');
function bawana_final_system_check() {
    // Hanya untuk admin dan hanya di homepage
    if (!current_user_can('manage_options') || !is_page_template('template-beranda.php')) {
        return;
    }

    echo '<script>';
    echo 'console.log("=== BAWANA SYSTEM CHECK ===");';
    echo 'console.log("Hot News: ' . (get_option('bawana_show_hot_news', 1) ? 'ENABLED' : 'DISABLED') . '");';
    echo 'console.log("Popular Articles: ' . (get_option('bawana_show_popular_articles', 1) ? 'ENABLED' : 'DISABLED') . '");';
    echo 'console.log("Featured Posts: ' . (get_option('bawana_show_featured_posts', 1) ? 'ENABLED' : 'DISABLED') . '");';
    echo 'console.log("Latest Stories: ' . (get_option('bawana_show_latest_stories', 1) ? 'ENABLED' : 'DISABLED') . '");';
    echo 'console.log("Video Section: ' . (get_option('bawana_show_video_section', 0) ? 'ENABLED' : 'DISABLED') . '");';
    echo 'console.log("Section Order: ' . (get_option('bawana_use_section_order', 0) ? 'DYNAMIC' : 'STATIC') . '");';
    echo 'console.log("Cache Buster: ' . time() . '");';
    echo 'console.log("=== END SYSTEM CHECK ===");';
    echo '</script>';
}

/* ----------------------------------------------------------------------------
 * AJAX PAGINATION UNTUK LATEST STORIES (OPTIONAL ENHANCEMENT)
 */
add_action('wp_ajax_bawana_load_latest_stories', 'bawana_ajax_load_latest_stories');
add_action('wp_ajax_nopriv_bawana_load_latest_stories', 'bawana_ajax_load_latest_stories');

function bawana_ajax_load_latest_stories() {
    // PERBAIKAN: Validasi nonce untuk keamanan
    if (!wp_verify_nonce($_POST['nonce'], 'bawana_ajax_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $paged = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $posts_per_page = 6;

    // DEBUG: Log request details
    error_log('AJAX Latest Stories Request - Page: ' . $paged . ', Posts per page: ' . $posts_per_page);

    $latest_query = new WP_Query(array(
        'posts_per_page' => $posts_per_page,
        'orderby' => 'date',
        'order' => 'DESC',
        'post_status' => 'publish',
        'paged' => $paged,
        'ignore_sticky_posts' => true
    ));

    $response = array();

    // DEBUG: Log query results
    error_log('AJAX Query Results - Found posts: ' . $latest_query->found_posts . ', Max pages: ' . $latest_query->max_num_pages);

    if ($latest_query->have_posts()) {
        ob_start();
        while ($latest_query->have_posts()) {
            $latest_query->the_post();

            $categories = get_the_category();
            $category_name = !empty($categories) ? $categories[0]->name : 'Nature';

            echo '<div class="latest-story-item">';
            echo '<div class="story-image">';
            echo '<a href="' . get_the_permalink() . '">';
            echo bawana_get_post_thumbnail(get_the_ID(), 'large', 'latest-story-thumb');
            echo '</a>';
            echo '</div>';
            echo '<div class="story-content">';
            echo '<div class="story-category">' . esc_html($category_name) . '</div>';
            echo '<h3 class="story-title"><a href="' . get_the_permalink() . '">' . get_the_title() . '</a></h3>';

            // Add excerpt for side-by-side layout
            $excerpt = get_the_excerpt();
            if (empty($excerpt)) {
                $excerpt = wp_trim_words(get_the_content(), 15, '...');
            }
            echo '<div class="story-excerpt">' . esc_html($excerpt) . '</div>';

            echo '</div>';
            echo '</div>';
        }
        $response['html'] = ob_get_clean();
        $response['max_pages'] = $latest_query->max_num_pages;
        wp_reset_postdata();
    } else {
        $response['html'] = '<p style="text-align: center; color: #666; padding: 40px;">Belum ada berita terkini.</p>';
        $response['max_pages'] = 0;
    }

    wp_send_json_success($response);
}

/* ----------------------------------------------------------------------------
 * JAVASCRIPT UNTUK AJAX PAGINATION LATEST STORIES
 */
add_action('wp_footer', 'bawana_latest_stories_ajax_script');
function bawana_latest_stories_ajax_script() {
    if (is_front_page() || is_page_template('template-beranda.php')) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Handle AJAX pagination clicks
            $(document).on('click', '.ajax-page-link', function(e) {
                e.preventDefault();

                var page = $(this).data('page');
                var $container = $('.latest-stories-grid');
                var $pagination = $('.bawana-pagination');
                var $loading = $('.latest-stories-loading');
                var $section = $('.latest-stories-section');

                // PERBAIKAN: Validasi elemen sebelum menggunakan
                console.log('Elements found:', {
                    container: $container.length,
                    section: $section.length,
                    loading: $loading.length,
                    pagination: $pagination.length
                });

                if (!$container.length || !$section.length) {
                    console.error('Latest Stories elements not found - container:', $container.length, 'section:', $section.length);
                    return;
                }

                // Show loading indicator
                if ($loading.length) {
                    $loading.show();
                }
                $container.css('opacity', '0.5');

                // PERBAIKAN: Scroll yang lebih aman
                var sectionOffset = $section.offset();
                if (sectionOffset && sectionOffset.top) {
                    $('html, body').animate({
                        scrollTop: sectionOffset.top - 100
                    }, 500);
                }

                console.log('Making AJAX request for page:', page);

                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    timeout: 10000, // 10 second timeout
                    data: {
                        action: 'bawana_load_latest_stories',
                        page: page,
                        nonce: '<?php echo wp_create_nonce('bawana_ajax_nonce'); ?>'
                    },
                    success: function(response) {
                        console.log('AJAX Response:', response);

                        if (response && response.success && response.data) {
                            // Update content
                            $container.html(response.data.html);

                            // Update pagination
                            updatePagination(page, response.data.max_pages);

                            // Hide loading
                            if ($loading.length) {
                                $loading.hide();
                            }
                            $container.css('opacity', '1');

                            // Update URL without page reload (SEO friendly)
                            if (history.pushState) {
                                var newUrl = page > 1 ?
                                    window.location.pathname + '?latest_page=' + page :
                                    window.location.pathname;
                                history.pushState({page: page}, '', newUrl);
                            }
                        } else {
                            console.error('AJAX Error:', response);
                            hideLoadingAndShowError('Data tidak valid dari server.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Request Failed:', {xhr: xhr, status: status, error: error});
                        hideLoadingAndShowError('Koneksi bermasalah. Silakan coba lagi.');
                    }
                });

                // Helper function untuk error handling
                function hideLoadingAndShowError(message) {
                    if ($loading.length) {
                        $loading.hide();
                    }
                    $container.css('opacity', '1');

                    // Show error message in container
                    $container.html('<div style="text-align: center; padding: 40px; color: #e74c3c;"><p>' + message + '</p><button onclick="location.reload()" style="background: #d4af37; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Refresh Halaman</button></div>');
                }
            });

            // Function to update pagination
            function updatePagination(currentPage, maxPages) {
                var $pagination = $('.bawana-pagination');

                // PERBAIKAN: Validasi elemen pagination
                if (!$pagination.length) {
                    console.log('Pagination element not found');
                    return;
                }

                var paginationHtml = '';

                // Only show pagination if more than 1 page
                if (maxPages > 1) {
                    // Previous button
                    if (currentPage > 1) {
                        paginationHtml += '<a href="#" class="page-numbers prev ajax-page-link" data-page="' + (currentPage - 1) + '">‹</a>';
                    }

                    // Page numbers
                    for (var i = 1; i <= maxPages; i++) {
                        if (i == currentPage) {
                            paginationHtml += '<span class="page-numbers current">' + i + '</span>';
                        } else {
                            paginationHtml += '<a href="#" class="page-numbers ajax-page-link" data-page="' + i + '">' + i + '</a>';
                        }
                    }

                    // Next button
                    if (currentPage < maxPages) {
                        paginationHtml += '<a href="#" class="page-numbers next ajax-page-link" data-page="' + (currentPage + 1) + '">›</a>';
                    }
                }

                $pagination.html(paginationHtml);
                $pagination.attr('data-current-page', currentPage);
                $pagination.attr('data-max-pages', maxPages);

                // Hide pagination if only 1 page
                if (maxPages <= 1) {
                    $pagination.hide();
                } else {
                    $pagination.show();
                }
            }
        });
        </script>

        <style>
        /* Loading animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .latest-stories-loading {
            text-align: center;
            padding: 20px;
            display: none;
        }

        /* Smooth transitions */
        .latest-stories-grid {
            transition: opacity 0.3s ease;
            min-height: 200px; /* Prevent layout shift */
        }

        /* Pagination hover effects */
        .ajax-page-link {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ajax-page-link:hover {
            text-decoration: none;
            background-color: #d4af37;
            color: white;
        }

        /* Error state styling */
        .latest-stories-error {
            text-align: center;
            padding: 40px;
            color: #e74c3c;
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            margin: 20px 0;
        }
        </style>
        <?php
    }
}

/* ----------------------------------------------------------------------------
 * PERBAIKAN KOMPREHENSIF FINAL - LAPORAN LENGKAP
 * Tanggal: <?php echo date('Y-m-d H:i:s'); ?>
 *
 * ✅ MASALAH LATEST STORIES TIDAK LOAD DIPERBAIKI:
 * - Fixed pagination parameter dari get_query_var('paged') ke $_GET['latest_page']
 * - Added ignore_sticky_posts untuk query yang lebih akurat
 * - Enhanced JavaScript debugging dengan console logging
 * - Fixed template structure untuk custom page template
 *
 * ✅ SEO PANEL ADMIN DIPERBAIKI DAN DITINGKATKAN:
 * - Added favicon upload functionality dengan file validation
 * - Enhanced favicon output di wp_head untuk Google search results
 * - Added proper enctype="multipart/form-data" untuk file upload
 * - Added favicon preview di admin panel
 * - Added multiple favicon link types untuk browser compatibility
 *
 * ✅ FAVICON UNTUK GOOGLE SEARCH RESULTS:
 * - Custom favicon upload system dengan validation
 * - Multiple favicon formats: icon, shortcut icon, apple-touch-icon
 * - Proper file handling dengan wp_upload_dir()
 * - Security validation untuk file types
 * - Visual preview di admin panel
 *
 * ✅ JAVASCRIPT ERROR DEBUGGING:
 * - Enhanced console logging untuk element detection
 * - Better error messages untuk troubleshooting
 * - Element validation sebelum DOM manipulation
 * - Safe offset checking untuk scroll functionality
 *
 * ✅ LAYOUT SIDE-BY-SIDE DIPERBAIKI:
 * - Images di sebelah kiri (40% width)
 * - Text content di sebelah kanan (60% width)
 * - Proper white background untuk text readability
 * - Responsive design untuk mobile
 *
 * ✅ KEAMANAN KOMPREHENSIF:
 * - File upload validation untuk favicon
 * - Nonce validation untuk semua forms
 * - Input sanitization dan output escaping
 * - Secure file handling dengan wp_mkdir_p()
 *
 * STATUS: LATEST STORIES & SEO PANEL DIPERBAIKI - SIAP TESTING ✅
 */
