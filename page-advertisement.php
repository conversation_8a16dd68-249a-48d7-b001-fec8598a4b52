<?php
/**
 * Template Name: Advertisement Page
 * Description: Halaman untuk informasi iklan dan kontak advertiser
 */

// Set page title to avoid undefined array key error
global $post;
if ($post) {
    $post->post_title = 'Advertisement';
}

get_header(); ?>

<div class="td-main-content-wrap td-container-wrap">
    <div class="td-container">
        <div class="td-crumb-container">
            <div class="td-crumb">
                <a href="<?php echo home_url(); ?>">Home</a>
                <span class="td-crumb-separator"> > </span>
                <span>Advertisement</span>
            </div>
        </div>

        <div class="td-pb-row">
            <div class="td-pb-span12">
                <div class="td-main-content">
                    <div class="td-ss-main-content">
                        <article class="post type-post status-publish format-standard">
                            <header class="td-post-title">
                                <h1 class="entry-title">Advertisement</h1>
                            </header>

                            <div class="td-post-content">
                                <div class="bawana-advertisement-page">
                                    <!-- Header Info -->
                                    <div class="advertisement-header">
                                        <p class="advertisement-intro">
                                            Untuk konsultasi tentang pemasangan iklan di situs web, silakan hubungi "Bagian Iklan" 
                                            <strong>+62 858-9293-9055</strong> atau isi form di bawah ini:
                                        </p>
                                    </div>

                                    <!-- Contact Form -->
                                    <div class="advertisement-form-container">
                                        <h3>Form Periklanan</h3>
                                        
                                        <form class="advertisement-form" method="post" action="#" id="advertisement-form">
                                            <div class="form-row">
                                                <div class="form-group half-width">
                                                    <label for="company-name">Nama</label>
                                                    <input type="text" id="company-name" name="company_name" required>
                                                </div>
                                                <div class="form-group half-width">
                                                    <label for="contact-person">Alamat</label>
                                                    <input type="text" id="contact-person" name="contact_person" required>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group half-width">
                                                    <label for="phone">Nomor</label>
                                                    <input type="tel" id="phone" name="phone" required>
                                                </div>
                                                <div class="form-group half-width">
                                                    <label for="telepon">Telepon</label>
                                                    <input type="tel" id="telepon" name="telepon">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group half-width">
                                                    <label for="email">Email</label>
                                                    <input type="email" id="email" name="email" placeholder="Email (opsional)">
                                                </div>
                                                <div class="form-group half-width">
                                                    <label for="website">Website</label>
                                                    <input type="url" id="website" name="website" placeholder="Website (opsional)">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group full-width">
                                                    <label for="message">Isi Surat</label>
                                                    <textarea id="message" name="message" rows="8" placeholder="Pemasangan iklan"></textarea>
                                                </div>
                                            </div>

                                            <!-- reCAPTCHA Placeholder -->
                                            <div class="form-row">
                                                <div class="form-group full-width">
                                                    <div class="recaptcha-container">
                                                        <div class="recaptcha-placeholder">
                                                            <input type="checkbox" id="recaptcha" name="recaptcha" required>
                                                            <label for="recaptcha">I'm not a robot</label>
                                                            <div class="recaptcha-logo">
                                                                <span>reCAPTCHA</span>
                                                                <small>Privacy - Terms</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group full-width">
                                                    <button type="submit" class="submit-btn">KIRIM</button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bawana-advertisement-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: var(--bawana-bg-primary);
    border-radius: 8px;
    box-shadow: var(--bawana-shadow-md);
}

.advertisement-header {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bawana-bg-light);
    border-radius: 8px;
    border-left: 4px solid var(--bawana-primary-blue);
}

.advertisement-intro {
    font-size: 16px;
    line-height: 1.6;
    color: var(--bawana-text-secondary);
    margin: 0;
}

.advertisement-form-container h3 {
    color: var(--bawana-text-primary);
    font-size: 20px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--bawana-primary-blue);
}

.advertisement-form {
    background: var(--bawana-bg-primary);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.half-width {
    flex: 1;
}

.form-group.full-width {
    width: 100%;
}

.form-group label {
    font-weight: 600;
    color: var(--bawana-text-primary);
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    padding: 12px 15px;
    border: 1px solid var(--bawana-border-color);
    border-radius: 6px;
    font-size: 14px;
    color: var(--bawana-text-secondary);
    background: var(--bawana-bg-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--bawana-primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.recaptcha-container {
    margin: 20px 0;
}

.recaptcha-placeholder {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid var(--bawana-border-color);
    border-radius: 6px;
    background: var(--bawana-bg-light);
    max-width: 300px;
}

.recaptcha-placeholder input[type="checkbox"] {
    margin-right: 12px;
    width: 20px;
    height: 20px;
}

.recaptcha-placeholder label {
    flex: 1;
    margin: 0;
    font-size: 14px;
    color: var(--bawana-text-secondary);
}

.recaptcha-logo {
    text-align: right;
    font-size: 12px;
    color: var(--bawana-text-muted);
}

.recaptcha-logo span {
    display: block;
    font-weight: 600;
    color: var(--bawana-primary-blue);
}

.submit-btn {
    background: var(--bawana-primary-blue);
    color: var(--bawana-bg-primary);
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.submit-btn:hover {
    background: var(--bawana-button-hover);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-group.half-width {
        width: 100%;
    }
    
    .bawana-advertisement-page {
        padding: 15px;
        margin: 10px;
    }
    
    .recaptcha-placeholder {
        max-width: 100%;
    }
}
</style>

<?php get_footer(); ?>
