<?php
/**
 * <PERSON>wana Hot News Test & Verification Script
 * 
 * This file helps verify that the Hot News admin panel is working correctly.
 * Access this file by adding ?bawana_test=1 to your admin URL
 */

// Security check
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

// Only run for administrators with test parameter
if (!current_user_can('manage_options') || !isset($_GET['bawana_test'])) {
    return;
}

add_action('admin_init', 'bawana_run_hot_news_test');
function bawana_run_hot_news_test() {
    if (!isset($_GET['bawana_test']) || $_GET['bawana_test'] !== '1') {
        return;
    }
    
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Bawana Hot News Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
            .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
            .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        </style>
    </head>
    <body>
        <h1>🔥 Bawana Hot News - Test & Verification</h1>
        
        <?php
        echo '<div class="test-result info">';
        echo '<h3>📋 System Information</h3>';
        echo '<p><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</p>';
        echo '<p><strong>Theme:</strong> ' . get_template() . ' (Child: ' . get_stylesheet() . ')</p>';
        echo '<p><strong>Current User:</strong> ' . wp_get_current_user()->display_name . '</p>';
        echo '<p><strong>User Capabilities:</strong> ' . (current_user_can('manage_options') ? 'Administrator ✅' : 'Limited Access ❌') . '</p>';
        echo '</div>';
        
        // Test 1: Function Existence
        echo '<div class="test-result ' . (function_exists('bawana_hot_news_admin_page') ? 'success' : 'error') . '">';
        echo '<h3>Test 1: Function Existence</h3>';
        echo '<p>bawana_hot_news_admin_page(): ' . (function_exists('bawana_hot_news_admin_page') ? '✅ EXISTS' : '❌ NOT FOUND') . '</p>';
        echo '<p>bawana_add_homepage_admin_menu(): ' . (function_exists('bawana_add_homepage_admin_menu') ? '✅ EXISTS' : '❌ NOT FOUND') . '</p>';
        echo '</div>';
        
        // Test 2: Options Check
        $hot_news_posts = get_option('bawana_hot_news_posts', array());
        echo '<div class="test-result ' . (!empty($hot_news_posts) ? 'success' : 'warning') . '">';
        echo '<h3>Test 2: Hot News Configuration</h3>';
        echo '<p><strong>Hot News Posts:</strong> ' . (!empty($hot_news_posts) ? count($hot_news_posts) . ' posts configured ✅' : 'Not configured ⚠️') . '</p>';
        if (!empty($hot_news_posts)) {
            echo '<p><strong>Post IDs:</strong> ' . implode(', ', $hot_news_posts) . '</p>';
            echo '<ul>';
            foreach ($hot_news_posts as $post_id) {
                $post = get_post($post_id);
                if ($post) {
                    echo '<li>' . esc_html($post->post_title) . ' (ID: ' . $post_id . ')</li>';
                } else {
                    echo '<li>❌ Post ID ' . $post_id . ' not found</li>';
                }
            }
            echo '</ul>';
        }
        echo '</div>';
        
        // Test 3: Menu Registration
        global $menu, $submenu;
        $menu_found = false;
        $submenu_found = false;
        
        foreach ($menu as $menu_item) {
            if (isset($menu_item[2]) && $menu_item[2] === 'bawana-homepage') {
                $menu_found = true;
                break;
            }
        }
        
        if (isset($submenu['bawana-homepage'])) {
            foreach ($submenu['bawana-homepage'] as $submenu_item) {
                if (isset($submenu_item[2]) && $submenu_item[2] === 'bawana-hot-news') {
                    $submenu_found = true;
                    break;
                }
            }
        }
        
        echo '<div class="test-result ' . ($menu_found && $submenu_found ? 'success' : 'error') . '">';
        echo '<h3>Test 3: Admin Menu Registration</h3>';
        echo '<p>Main Menu (Beranda Bawana): ' . ($menu_found ? '✅ REGISTERED' : '❌ NOT FOUND') . '</p>';
        echo '<p>Submenu (Berita Terpanas): ' . ($submenu_found ? '✅ REGISTERED' : '❌ NOT FOUND') . '</p>';
        echo '</div>';
        
        // Test 4: URL Access
        $admin_url = admin_url('admin.php?page=bawana-hot-news');
        echo '<div class="test-result info">';
        echo '<h3>Test 4: Direct Access</h3>';
        echo '<p><strong>Admin URL:</strong> <a href="' . $admin_url . '" target="_blank">' . $admin_url . '</a></p>';
        echo '<p><strong>Homepage URL:</strong> <a href="' . home_url() . '" target="_blank">' . home_url() . '</a></p>';
        echo '</div>';
        
        // Test 5: Template Integration
        $template_file = get_stylesheet_directory() . '/template-beranda.php';
        $template_exists = file_exists($template_file);
        
        echo '<div class="test-result ' . ($template_exists ? 'success' : 'error') . '">';
        echo '<h3>Test 5: Template Integration</h3>';
        echo '<p>Template File: ' . ($template_exists ? '✅ EXISTS' : '❌ NOT FOUND') . '</p>';
        
        if ($template_exists) {
            $template_content = file_get_contents($template_file);
            $has_hot_news_code = strpos($template_content, 'bawana_hot_news_posts') !== false;
            echo '<p>Hot News Integration: ' . ($has_hot_news_code ? '✅ INTEGRATED' : '❌ NOT INTEGRATED') . '</p>';
        }
        echo '</div>';
        
        // Action Buttons
        echo '<div class="test-result info">';
        echo '<h3>🚀 Quick Actions</h3>';
        echo '<p>';
        echo '<a href="' . admin_url('admin.php?page=bawana-hot-news') . '" style="background: #d4af37; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🔥 Open Hot News Admin</a>';
        echo '<a href="' . admin_url('admin.php?page=bawana-homepage') . '" style="background: #1e3a8a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">⚙️ Homepage Settings</a>';
        echo '<a href="' . home_url() . '" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;" target="_blank">🏠 View Homepage</a>';
        echo '</p>';
        echo '</div>';
        
        ?>
    </body>
    </html>
    <?php
    exit;
}
