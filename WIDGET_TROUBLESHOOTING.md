# 🔧 Panduan Troubleshooting Widget Bawana News

## Masalah: Widget Tidak Muncul di Sidebar

Jika widget Bawana News (Live Streaming, Berita Populer, Talkshow) tidak muncul di sidebar website, ikuti langkah-langkah berikut:

### 🚀 Solusi Otomatis (Tercepat)

1. **<PERSON>suk ke WordPress Admin**
2. **Pergi ke Theme Panel → Widget Troubleshooting**
3. **Klik tombol "Reset & Setup Ulang Widget"**
4. **Refresh halaman website untuk melihat hasilnya**

### 🛠️ Solusi Manual

#### Langkah 1: Cek Widget di Admin
1. Pergi ke **Appearance → Widgets**
2. Cari widget berikut di daftar "Available Widgets":
   - ✅ Bawana Live Streaming
   - ✅ Bawana Berita Populer  
   - ✅ Bawana Talkshow

#### Langkah 2: Tambahkan Widget ke Sidebar
1. **Drag** widget dari "Available Widgets"
2. **Drop** ke sidebar **"Newspaper default"**
3. **Klik Save**

#### Langkah 3: Verifikasi <PERSON>
1. **Live Streaming & Talkshow**: Tambahkan konten melalui **Theme Panel → Video Sidebar**
2. **Berita Populer**: Otomatis menggunakan post yang ada

### 🔍 Debug Mode

Untuk melihat informasi debug:
1. Tambahkan `?debug_widgets=1` di URL website
2. Contoh: `https://yoursite.com/?debug_widgets=1`
3. Kotak debug akan muncul di pojok kanan bawah

### 📱 Tampilan Widget

#### Live Streaming Widget
- **Background**: Gradient biru
- **Konten**: Video embed dari YouTube/Facebook
- **Placeholder**: Muncul jika belum ada konten

#### Berita Populer Widget  
- **Background**: Putih dengan border emas
- **Konten**: 5 berita terpopuler dengan thumbnail
- **Fallback**: Berita terbaru jika belum ada komentar

#### Talkshow Widget
- **Background**: Gradient emas
- **Konten**: Video embed talkshow
- **Placeholder**: Muncul jika belum ada konten

### ⚠️ Catatan Penting

1. **Widget akan selalu muncul** meskipun belum ada konten (menampilkan placeholder)
2. **Placeholder memberikan instruksi** cara menambahkan konten
3. **Styling otomatis** sesuai branding Bawana News
4. **Responsive** untuk semua ukuran layar

### 🆘 Jika Masalah Berlanjut

1. **Clear cache** browser dan plugin caching
2. **Deactivate/Activate** child theme
3. **Reset widget** melalui troubleshooting page
4. **Cek error log** WordPress untuk pesan error

### 📞 Kontak Support

Jika semua langkah di atas tidak berhasil, hubungi developer dengan informasi:
- URL website
- Screenshot masalah
- Hasil debug mode
- Versi WordPress dan theme
