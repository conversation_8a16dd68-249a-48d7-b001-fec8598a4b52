<?php
/**
 * Template Name: Contact Page
 * Description: Halaman kontak Bawana News
 */

// Set page title to avoid undefined array key error
global $post;
if ($post) {
    $post->post_title = 'Contact';
}

get_header(); ?>

<div class="td-main-content-wrap td-container-wrap">
    <div class="td-container">
        <div class="td-crumb-container">
            <div class="td-crumb">
                <a href="<?php echo home_url(); ?>">Home</a>
                <span class="td-crumb-separator"> > </span>
                <span>Contact</span>
            </div>
        </div>

        <div class="td-pb-row">
            <div class="td-pb-span12">
                <div class="td-main-content">
                    <div class="td-ss-main-content">
                        <article class="post type-post status-publish format-standard">
                            <header class="td-post-title">
                                <h1 class="entry-title">Contact</h1>
                            </header>

                            <div class="td-post-content">
                                <div class="bawana-contact-page">
                                    <!-- Contact Header -->
                                    <div class="contact-header">
                                        <h2>BAWANA NEWS</h2>
                                        <p class="contact-tagline">Portal Berita Terpercaya - Mendunia</p>
                                        <p class="contact-intro">
                                            Hubungi tim redaksi Bawana News untuk informasi, saran, kritik, atau kerjasama. 
                                            Kami siap melayani Anda dengan profesional.
                                        </p>
                                    </div>

                                    <div class="contact-content-wrapper">
                                        <!-- Contact Information -->
                                        <div class="contact-info-section">
                                            <h3>Informasi Kontak</h3>
                                            
                                            <div class="contact-details">
                                                <div class="contact-item">
                                                    <div class="contact-icon">
                                                        <i class="td-icon-location"></i>
                                                    </div>
                                                    <div class="contact-text">
                                                        <h4>Alamat Redaksi</h4>
                                                        <p>Jl. Arya Salingsingan No.76<br>
                                                        Kasugengan Kidul, Kec. Depok<br>
                                                        Kabupaten Cirebon, Jawa Barat 45653</p>
                                                    </div>
                                                </div>

                                                <div class="contact-item">
                                                    <div class="contact-icon">
                                                        <i class="td-icon-phone"></i>
                                                    </div>
                                                    <div class="contact-text">
                                                        <h4>Telepon</h4>
                                                        <p><strong>+62 858-9293-9055</strong></p>
                                                        <p class="contact-note">Senin - Jumat: 09:00 - 17:00 WIB</p>
                                                    </div>
                                                </div>

                                                <div class="contact-item">
                                                    <div class="contact-icon">
                                                        <i class="td-icon-mail"></i>
                                                    </div>
                                                    <div class="contact-text">
                                                        <h4>Email</h4>
                                                        <p><EMAIL></p>
                                                        <p class="contact-note">Untuk keperluan redaksi dan berita</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Department Contacts -->
                                            <div class="department-contacts">
                                                <h4>Kontak Departemen</h4>
                                                <div class="dept-grid">
                                                    <div class="dept-item">
                                                        <h5>Redaksi</h5>
                                                        <p>Babussalam<br><em>Pimpinan Redaksi</em></p>
                                                    </div>
                                                    <div class="dept-item">
                                                        <h5>Umum</h5>
                                                        <p>Darsono<br><em>Pimpinan Umum</em></p>
                                                    </div>
                                                    <div class="dept-item">
                                                        <h5>IT Support</h5>
                                                        <p>Moh Mujiburrahman<br><em>Technical Support</em></p>
                                                    </div>
                                                    <div class="dept-item">
                                                        <h5>Iklan</h5>
                                                        <p>+62 858-9293-9055<br><em>Pemasangan Iklan</em></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Contact Form -->
                                        <div class="contact-form-section">
                                            <h3>Kirim Pesan</h3>
                                            
                                            <form class="bawana-contact-form" method="post" action="#" id="contact-form">
                                                <div class="form-row">
                                                    <div class="form-group half-width">
                                                        <label for="contact-name">Nama Lengkap *</label>
                                                        <input type="text" id="contact-name" name="contact_name" required>
                                                    </div>
                                                    <div class="form-group half-width">
                                                        <label for="contact-phone">Nomor Telepon *</label>
                                                        <input type="tel" id="contact-phone" name="contact_phone" required>
                                                    </div>
                                                </div>

                                                <div class="form-row">
                                                    <div class="form-group full-width">
                                                        <label for="contact-email">Email</label>
                                                        <input type="email" id="contact-email" name="contact_email" placeholder="Email Anda (opsional)">
                                                    </div>
                                                </div>

                                                <div class="form-row">
                                                    <div class="form-group full-width">
                                                        <label for="contact-subject">Subjek *</label>
                                                        <select id="contact-subject" name="contact_subject" required>
                                                            <option value="">Pilih Subjek</option>
                                                            <option value="berita">Laporan Berita</option>
                                                            <option value="kritik">Kritik & Saran</option>
                                                            <option value="kerjasama">Kerjasama</option>
                                                            <option value="iklan">Pemasangan Iklan</option>
                                                            <option value="teknis">Masalah Teknis</option>
                                                            <option value="lainnya">Lainnya</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-row">
                                                    <div class="form-group full-width">
                                                        <label for="contact-message">Pesan *</label>
                                                        <textarea id="contact-message" name="contact_message" rows="6" placeholder="Tulis pesan Anda di sini..." required></textarea>
                                                    </div>
                                                </div>

                                                <!-- reCAPTCHA Placeholder -->
                                                <div class="form-row">
                                                    <div class="form-group full-width">
                                                        <div class="recaptcha-placeholder">
                                                            <div class="recaptcha-box">
                                                                <input type="checkbox" id="recaptcha-check">
                                                                <label for="recaptcha-check">Saya bukan robot</label>
                                                                <div class="recaptcha-logo">reCAPTCHA</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-row">
                                                    <div class="form-group full-width">
                                                        <button type="submit" class="contact-submit-btn">
                                                            <i class="td-icon-mail"></i>
                                                            Kirim Pesan
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>

                                    <!-- Additional Info -->
                                    <div class="contact-footer">
                                        <div class="contact-note-box">
                                            <h4>Catatan Penting</h4>
                                            <ul>
                                                <li>Untuk laporan berita mendesak, silakan hubungi langsung melalui telepon</li>
                                                <li>Pesan akan dibalas dalam 1x24 jam pada hari kerja</li>
                                                <li>Untuk pemasangan iklan, silakan hubungi bagian iklan di +62 858-9293-9055</li>
                                                <li>Kritik dan saran Anda sangat berharga untuk kemajuan Bawana News</li>
                                            </ul>
                                        </div>
                                    </div>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bawana-contact-page {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.contact-header {
    text-align: center;
    background: linear-gradient(135deg, var(--bawana-bg-light), #f0f7ff);
    padding: 40px 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid var(--bawana-border-color);
}

.contact-header h2 {
    color: var(--bawana-primary-blue);
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.contact-tagline {
    color: var(--bawana-primary-gold);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    font-style: italic;
}

.contact-intro {
    color: var(--bawana-text-secondary);
    font-size: 16px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.contact-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.contact-info-section,
.contact-form-section {
    background: var(--bawana-bg-primary);
    padding: 30px;
    border-radius: 12px;
    border: 1px solid var(--bawana-border-color);
    box-shadow: var(--bawana-shadow-sm);
}

.contact-info-section h3,
.contact-form-section h3 {
    color: var(--bawana-text-primary);
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--bawana-primary-blue);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 15px;
    background: var(--bawana-bg-light);
    border-radius: 8px;
    border-left: 4px solid var(--bawana-primary-blue);
}

.contact-icon {
    background: var(--bawana-primary-blue);
    color: var(--bawana-bg-primary);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 20px;
}

.contact-text h4 {
    color: var(--bawana-text-primary);
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 8px;
}

.contact-text p {
    color: var(--bawana-text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 5px;
}

.contact-note {
    font-style: italic;
    color: var(--bawana-text-muted);
    font-size: 13px;
}

.department-contacts {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--bawana-border-light);
}

.department-contacts h4 {
    color: var(--bawana-text-primary);
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 15px;
}

.dept-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.dept-item {
    background: var(--bawana-bg-primary);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--bawana-border-light);
}

.dept-item h5 {
    color: var(--bawana-primary-blue);
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 8px;
}

.dept-item p {
    color: var(--bawana-text-secondary);
    font-size: 13px;
    line-height: 1.4;
    margin: 0;
}

/* Form Styling */
.bawana-contact-form {
    width: 100%;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.half-width {
    flex: 1;
}

.form-group.full-width {
    width: 100%;
}

.form-group label {
    color: var(--bawana-text-primary);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid var(--bawana-border-color);
    border-radius: 8px;
    font-size: 14px;
    background: var(--bawana-bg-primary);
    color: var(--bawana-text-secondary);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--bawana-primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.recaptcha-placeholder {
    margin: 15px 0;
}

.recaptcha-box {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid var(--bawana-border-color);
    border-radius: 8px;
    background: var(--bawana-bg-light);
    position: relative;
}

.recaptcha-box input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.recaptcha-logo {
    position: absolute;
    right: 15px;
    font-size: 12px;
    color: var(--bawana-text-muted);
    font-weight: 600;
}

.contact-submit-btn {
    background: linear-gradient(135deg, var(--bawana-primary-blue), var(--bawana-accent-blue));
    color: var(--bawana-bg-primary);
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
}

.contact-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--bawana-shadow-md);
}

.contact-footer {
    margin-top: 30px;
}

.contact-note-box {
    background: var(--bawana-bg-light);
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid var(--bawana-primary-gold);
}

.contact-note-box h4 {
    color: var(--bawana-text-primary);
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 15px;
}

.contact-note-box ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact-note-box li {
    color: var(--bawana-text-secondary);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.contact-note-box li::before {
    content: '•';
    color: var(--bawana-primary-gold);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .contact-info-section,
    .contact-form-section {
        padding: 20px;
    }
    
    .contact-header {
        padding: 30px 20px;
    }
    
    .contact-header h2 {
        font-size: 24px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .dept-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .bawana-contact-page {
        padding: 15px;
        margin: 10px;
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
</style>

<?php get_footer(); ?>
