/*
Theme Name: Newspaper Child - Bawana News
Description: Child theme untuk Newspaper dengan kustomisasi Bawana News
Author: Bawana News Team
Template: Newspaper
Version: 1.0.0
*/

@import url("../Newspaper/style.css");

/* ============================================================================
   BAWANA NEWS BRANDING & COLORS - CSS VARIABLES
   ============================================================================ */

:root {
    /* LIGHT PROFESSIONAL THEME - Bawana News */
    --bawana-primary-blue: #1e3a8a;        /* Biru Tua Primary */
    --bawana-primary-gold: #d4af37;        /* Emas Primary */
    --bawana-accent-blue: #3b82f6;         /* Biru Aksen */

    /* Background Colors - Light Theme */
    --bawana-bg-primary: #ffffff;          /* Background Utama */
    --bawana-bg-secondary: #f8fafc;        /* Background Sekunder */
    --bawana-bg-light: #f1f5f9;            /* Background Terang */

    /* Text Colors - High Contrast for Readability */
    --bawana-text-primary: #1a1a1a;        /* Teks Utama */
    --bawana-text-secondary: #374151;      /* Teks Sekunder */
    --bawana-text-muted: #6b7280;          /* Teks Muted */
    --bawana-text-light: #9ca3af;          /* Teks Terang */

    /* Border & Dividers */
    --bawana-border-color: #e5e7eb;        /* Border Utama */
    --bawana-border-light: #f3f4f6;        /* Border Terang */

    /* Interactive Elements */
    --bawana-link-color: #1e3a8a;          /* Warna Link */
    --bawana-link-hover: #1e40af;          /* Warna Link Hover */
    --bawana-button-primary: #1e3a8a;      /* Tombol Primary */
    --bawana-button-hover: #1e40af;        /* Tombol Hover */

    /* Shadows - Subtle for Light Theme */
    --bawana-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --bawana-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --bawana-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Missing Variables Definitions */
    --bawana-light-gold: #f0c850; /* Contoh nilai, sesuaikan jika ada nilai spesifik */
    --bawana-white: #ffffff;
    --bawana-dark-gray: #333333;
    --bawana-light-gray: #eeeeee;
    --bawana-gradient-primary: linear-gradient(135deg, #1e3a8a, #3b82f6);
    --bawana-gradient-gold: linear-gradient(135deg, #d4af37, #f0c850);
    --bawana-dark-blue: #1a2b5b; /* Contoh nilai, sesuaikan jika ada nilai spesifik */
    --bawana-text-color: #333333; /* Menggunakan dark-gray sebagai default */
    --bawana-gray: #666666; /* Menggunakan muted text sebagai default */
}

/* ============================================================================
   GLOBAL RESET & BASE STYLES - LIGHT PROFESSIONAL THEME
   ============================================================================ */

/* Reset ke Light Theme - Background Putih */
body {
    background-color: var(--bawana-bg-primary) !important;
    color: var(--bawana-text-primary) !important;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
}

/* Container utama - Background Putih */
.td-main-content-wrap,
.td-container,
.td-main-content {
    background-color: var(--bawana-bg-primary) !important;
}

/* Sidebar - Background Putih */
.td-pb-span4,
.td-pb-span8 {
    background-color: var(--bawana-bg-primary) !important;
}

/* Reset header background dan hapus area kosong */
.td-header-wrap {
    background-color: var(--bawana-bg-primary);
    margin: 0;
    padding: 0;
    min-height: auto;
}

.td-pb-row {
    background-color: var(--bawana-bg-primary);
}

/* Hapus area kosong di atas header */
.td-banner-wrap-full {
    margin: 0;
    padding: 0;
    min-height: auto;
}

.td-logo-wrap-full {
    margin: 0;
    padding: 0;
    min-height: auto;
}

/* Content area styling - Light Professional */
.td-post-content,
.td-post-text-content,
.td-post-header,
.td-post-title,
.entry-title,
.td-post-title a,
.entry-title a {
    color: var(--bawana-text-primary) !important;
}

/* Post titles hover effect */
.td-post-title a:hover,
.entry-title a:hover {
    color: var(--bawana-primary-blue) !important;
    transition: color 0.3s ease;
}

/* Post meta information */
.td-post-author-name,
.td-post-date,
.td-post-views,
.td-post-comments {
    color: var(--bawana-text-muted) !important;
}

/* Post meta links */
.td-post-author-name a,
.td-post-date a {
    color: var(--bawana-text-muted) !important;
}

.td-post-author-name a:hover,
.td-post-date a:hover {
    color: var(--bawana-primary-blue) !important;
}

/* ============================================================================
   GLOBAL THEME COLOR OVERRIDES
   ============================================================================ */

/* Override tema default colors dengan Bawana News branding */
.td-theme-wrap a,
.td-post-content a {
    color: var(--bawana-link-color);
}

.td-theme-wrap a:hover,
.td-post-content a:hover {
    color: var(--bawana-link-hover);
}

/* ============================================================================
   HEADER DESIGN - TRAVEL BIRDIE STYLE (MENU ATAS, LOGO TENGAH BAWAH)
   ============================================================================ */

/* STRUKTUR HEADER BARU - Travel Birdie Style */
.bawana-header-structure {
    background: var(--bawana-bg-primary) !important;
    width: 100%;
    text-align: center;
    padding: 0;
    margin: 0;
}

/* TOP MENU BAR - Ramping seperti Travel Birdie */
.bawana-top-menu {
    background: linear-gradient(135deg, var(--bawana-primary-blue) 0%, var(--bawana-accent-blue) 100%);
    border-bottom: 1px solid var(--bawana-primary-blue);
    padding: 8px 20px;
    text-align: center;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 4px rgba(30, 58, 138, 0.1);
    min-height: 40px;
}

.bawana-main-menu {
    display: flex;
    flex-direction: row;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 20px;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
}

.bawana-main-menu li {
    list-style: none;
    margin: 0;
    padding: 0;
    display: inline-block;
    float: none;
}

/* Force horizontal layout - override any vertical styling */
.bawana-main-menu,
.bawana-main-menu ul,
.bawana-main-menu li {
    display: flex !important;
    flex-direction: row !important;
}

.bawana-main-menu ul {
    flex-direction: row !important;
    gap: 30px;
}

/* Override any theme CSS that might make menu vertical */
.bawana-top-menu .bawana-main-menu li {
    width: auto !important;
    height: auto !important;
    display: inline-flex !important;
}

/* Ensure menu container is horizontal */
.bawana-main-menu {
    width: auto !important;
    max-width: none !important;
    position: static !important;
}

/* Fix for WordPress menu output */
.bawana-top-menu .menu,
.bawana-top-menu .bawana-main-menu {
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    list-style: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.bawana-main-menu li a {
    color: #ffffff !important;
    text-decoration: none;
    font-weight: 500;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    display: inline-block;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.bawana-main-menu li a:hover,
.bawana-main-menu li a:focus,
.bawana-main-menu li.current-menu-item a {
    color: var(--bawana-primary-gold);
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* LOGO SECTION - Logo di tengah bawah */
.bawana-logo-section {
    background: var(--bawana-bg-primary);
    padding: 25px 0 20px 0;
    text-align: center;
    border-bottom: 2px solid var(--bawana-primary-blue);
}

.bawana-logo-section img,
.bawana-logo-section .custom-logo-link img {
    max-height: 150px;
    width: auto;
    margin: 0 auto;
    display: block;
}

.bawana-fallback-logo {
    max-height: 150px;
    width: auto;
    margin: 0 auto;
    display: block;
}

/* HIDE ORIGINAL HEADER ELEMENTS - SEMUA MENU LAMA */
.td-header-menu-wrap-full,
.td-header-menu-wrap,
.td-header-row,
.td-header-main-menu,
.td-main-menu,
.sf-menu,
.td-header-menu-social,
.td-header-sp-top-menu {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Override parent theme header - hapus area kosong */
.td-header-wrap .td-header-sp-logo {
    width: 100%;
    text-align: center;
    padding: 0;
    margin: 0;
    min-height: auto;
}

/* Hapus area kosong dari container */
.td-container-wrap {
    margin: 0;
    padding: 0;
}

.td-header-style-1 {
    margin: 0;
    padding: 0;
    min-height: auto;
}

/* RESPONSIVE DESIGN FOR NEW HEADER */
@media (max-width: 767px) {
    .bawana-main-menu {
        flex-wrap: wrap;
        gap: 12px;
        padding: 0 15px;
    }

    .bawana-main-menu li a {
        font-size: 11px;
        padding: 4px 8px;
    }

    .bawana-logo-section {
        padding: 15px 0 10px 0;
    }

    .bawana-logo-section img,
    .bawana-logo-section .custom-logo-link img,
    .bawana-fallback-logo {
        max-height: 120px;
    }
}

@media (max-width: 480px) {
    .bawana-main-menu {
        gap: 8px;
        padding: 0 10px;
    }

    .bawana-main-menu li a {
        font-size: 10px;
        padding: 3px 6px;
    }

    .bawana-logo-section img,
    .bawana-logo-section .custom-logo-link img,
    .bawana-fallback-logo {
        max-height: 100px;
    }
}

/* OLD HEADER STYLES - REMOVED FOR CLEANER CODE */

/* Search DIHAPUS - Tidak diperlukan */

/* Header lama DIHAPUS - Tidak diperlukan */

/* Fix untuk teks yang tidak terlihat di header */
.td-header-top-menu *,
.td-header-top-menu-full * {
    color: var(--bawana-white) !important;
}

.td-header-top-menu a,
.td-header-top-menu-full a {
    color: var(--bawana-white) !important;
    text-decoration: none;
}

.td-header-top-menu a:hover,
.td-header-top-menu-full a:hover {
    color: var(--bawana-light-gold) !important;
}

/* Menu Items */
.td-menu-background:before {
    background: var(--bawana-gradient-primary);
}

.sf-menu > li > a {
    color: var(--bawana-white) !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    position: relative;
}

.sf-menu > li > a:hover,
.sf-menu > li:hover > a {
    color: var(--bawana-light-gold) !important;
    transform: translateY(-1px);
}

.sf-menu > li > a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--bawana-light-gold);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.sf-menu > li:hover > a::after {
    width: 80%;
}

/* Submenu styling */
.sf-menu ul {
    background: var(--bawana-white);
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.15);
    border-top: 3px solid var(--bawana-primary-gold);
}

.sf-menu ul li a {
    color: var(--bawana-dark-gray) !important;
    text-shadow: none;
    border-bottom: 1px solid var(--bawana-light-gray);
}

.sf-menu ul li a:hover {
    background: var(--bawana-light-gray);
    color: var(--bawana-primary-blue) !important;
}

/* Buttons */
.td-read-more a,
.td-post-next-prev a,
.td-page-nav a {
    background: var(--bawana-gradient-primary);
    color: var(--bawana-white);
    border: none;
}

.td-read-more a:hover,
.td-post-next-prev a:hover,
.td-page-nav a:hover {
    background: var(--bawana-gradient-gold);
    transform: translateY(-1px);
}

/* NAVIGASI BERITA SELANJUTNYA/SEBELUMNYA - FORCE WHITE TEXT */
.td-post-next-prev,
.td-post-next-prev a,
.td-post-next-prev span,
.post-navigation,
.post-navigation a,
.post-navigation span,
.nav-links,
.nav-links a,
.nav-links span {
    color: #ffffff !important;
    text-decoration: none !important;
}

.td-post-next-prev a:hover,
.post-navigation a:hover,
.nav-links a:hover {
    color: #ffffff !important;
    opacity: 0.9;
}

/* Widget Titles Global */
.widget .block-title > span {
    background: var(--bawana-gradient-primary) !important;
    color: var(--bawana-white) !important;
}

/* Category Tags */
.td-post-category,
.td-category-pos-above .td-post-category {
    background: var(--bawana-primary-gold);
    color: var(--bawana-white);
}

.td-post-category:hover {
    background: var(--bawana-light-gold);
}

/* ============================================================================
   PERBAIKAN KONTRAS & EFEK VISUAL BAWANA NEWS
   ============================================================================ */

/* Breadcrumb - Light Professional Style */
.td-breadcrumbs,
.td-crumb-container {
    background: var(--bawana-primary-gold) !important;
    padding: 12px 20px !important;
    border-radius: 6px !important;
    margin-bottom: 20px !important;
    box-shadow: var(--bawana-shadow-sm) !important;
    border: none !important;
}

.td-breadcrumbs .td-breadcrumb-sep,
.td-breadcrumbs a,
.td-breadcrumbs span,
.td-crumb-container .td-breadcrumb-sep,
.td-crumb-container a,
.td-crumb-container span,
.entry-crumbs a,
.entry-crumbs span {
    color: var(--bawana-bg-primary) !important;
    text-shadow: none !important;
    font-weight: 600 !important;
}

.td-breadcrumbs a:hover,
.td-crumb-container a:hover,
.entry-crumbs a:hover {
    color: rgba(255, 255, 255, 0.8) !important;
    transition: color 0.3s ease;
    text-decoration: underline;
}

/* Category Tags - Light Professional Style */
.td-category,
.td-post-category,
.entry-crumbs {
    background: var(--bawana-primary-blue) !important;
    color: #ffffff !important; /* FORCE WHITE TEXT FOR CATEGORIES */
    padding: 4px 12px !important;
    border-radius: 15px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.td-category:hover,
.td-post-category:hover {
    background: var(--bawana-button-hover) !important;
    color: #ffffff !important; /* FORCE WHITE TEXT ON HOVER */
    transform: translateY(-1px) !important;
    box-shadow: var(--bawana-shadow-md) !important;
}

/* KATEGORI DI HALAMAN POSTINGAN - FORCE WHITE TEXT */
.single .td-category,
.single .td-post-category,
.single .entry-category,
.single .td-category a,
.single .td-post-category a,
.single .entry-category a,
.td-post-content .td-category,
.td-post-content .td-post-category,
.td-post-content .entry-category,
.td-post-content .td-category a,
.td-post-content .td-post-category a,
.td-post-content .entry-category a {
    color: #ffffff !important;
    text-decoration: none !important;
}

.single .td-category:hover,
.single .td-post-category:hover,
.single .entry-category:hover,
.single .td-category a:hover,
.single .td-post-category a:hover,
.single .entry-category a:hover {
    color: #ffffff !important;
    opacity: 0.9;
}

/* BREADCRUMB & KATEGORI GLOBAL - FORCE WHITE TEXT */
.td-crumb-container,
.td-crumb-container a,
.entry-crumbs,
.entry-crumbs a,
.td-module-meta-info .td-category,
.td-module-meta-info .td-category a,
.td-module-meta-info .entry-category,
.td-module-meta-info .entry-category a,
.td-category li a,
.entry-category a {
    color: #ffffff !important;
    text-decoration: none !important;
}

.td-crumb-container a:hover,
.entry-crumbs a:hover,
.td-module-meta-info .td-category a:hover,
.td-module-meta-info .entry-category a:hover,
.td-category li a:hover,
.entry-category a:hover {
    color: #ffffff !important;
    opacity: 0.8;
}

/* Post Title & Content - Enhanced Readability */
.td-post-header .entry-title {
    color: var(--bawana-dark-blue) !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    transition: color 0.3s ease;
}

.td-post-content h1,
.td-post-content h2,
.td-post-content h3,
.td-post-content h4,
.td-post-content h5,
.td-post-content h6 {
    color: var(--bawana-dark-blue) !important;
    margin-bottom: 15px;
    line-height: 1.4;
}

.td-post-content p,
.td-post-content {
    color: var(--bawana-text-color) !important;
    line-height: 1.7;
    font-size: 16px;
}

/* Post Meta - Better Contrast */
.td-module-meta-info,
.td-post-author-name,
.td-post-date,
.td-post-views,
.td-module-comments a {
    color: var(--bawana-gray) !important;
    font-size: 13px;
}

.td-module-meta-info a,
.td-post-author-name a {
    color: var(--bawana-primary-blue) !important;
    transition: all 0.3s ease;
    text-decoration: none;
}

.td-module-meta-info a:hover,
.td-post-author-name a:hover {
    color: var(--bawana-primary-gold) !important;
    transform: translateX(2px);
}

/* Enhanced Button Effects */
.td-read-more a {
    background: var(--bawana-gradient-primary) !important;
    color: var(--bawana-white) !important;
    padding: 10px 20px !important;
    border-radius: 25px !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    box-shadow: 0 3px 10px rgba(30, 58, 138, 0.2) !important;
    transition: all 0.3s ease !important;
    display: inline-block !important;
    border: none !important;
}

.td-read-more a:hover {
    background: var(--bawana-gradient-gold) !important;
    color: var(--bawana-dark-blue) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3) !important;
}

/* Module Hover Effects */
.td-module-container:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

.td-module-thumb:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* Link Hover Effects */
.td-module-title a {
    color: var(--bawana-dark-blue) !important;
    transition: color 0.3s ease;
}

.td-module-title a:hover {
    color: var(--bawana-primary-blue) !important;
}

/* Search Box DIHAPUS - Tidak diperlukan */

/* Pagination Enhancement */
.page-nav .current,
.page-nav a {
    background: var(--bawana-white);
    color: var(--bawana-primary-blue) !important;
    border: 2px solid var(--bawana-primary-blue);
    transition: all 0.3s ease;
}

.page-nav .current,
.page-nav a:hover {
    background: var(--bawana-primary-blue);
    color: var(--bawana-white) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(30, 58, 138, 0.2);
}

/* Comments Section */
.td-comments .td-pb-padding-side {
    background: var(--bawana-white);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.comment-form input[type="text"],
.comment-form input[type="email"],
.comment-form textarea {
    border: 2px solid var(--bawana-light-gray);
    border-radius: 5px;
    transition: border-color 0.3s ease;
    color: var(--bawana-dark-gray) !important;
}

.comment-form input[type="text"]:focus,
.comment-form input[type="email"]:focus,
.comment-form textarea:focus {
    border-color: var(--bawana-primary-blue);
    box-shadow: 0 0 5px rgba(30, 58, 138, 0.2);
}

.comment-form input[type="submit"] {
    background: var(--bawana-gradient-primary) !important;
    color: var(--bawana-white) !important;
    border: none !important;
    border-radius: 25px !important;
    padding: 12px 25px !important;
    transition: all 0.3s ease !important;
}

.comment-form input[type="submit"]:hover {
    background: var(--bawana-gradient-gold) !important;
    color: var(--bawana-dark-blue) !important;
    transform: translateY(-2px) !important;
}

/* ============================================================================
   CUSTOM CSS UNTUK BAWANA NEWS
   ============================================================================ */
.td-logo-text {
    font-weight: bold;
    color: var(--bawana-primary-blue);
}

.td-tagline-text {
    color: var(--bawana-gray);
    font-size: 12px;
}

/* Header customization */
.td-header-wrap {
    border-bottom: 3px solid var(--bawana-primary-blue);
}

/* Footer customization */
.td-sub-footer-copy {
    text-align: center;
    color: var(--bawana-gray);
}

/* ============================================================================
   FOOTER STYLING - LIGHT PROFESSIONAL
   ============================================================================ */

/* Footer - Light Professional Style */
.td-footer-wrapper {
    background: var(--bawana-bg-primary) !important;
    color: var(--bawana-text-secondary) !important;
    border-top: 1px solid var(--bawana-border-color) !important;
    position: relative;
}

.td-footer-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--bawana-primary-blue);
}

.td-footer-wrapper .td-footer-bottom-full {
    background: var(--bawana-bg-light) !important;
    border-top: 1px solid var(--bawana-border-color) !important;
}

.td-footer-wrapper a {
    color: var(--bawana-primary-blue) !important;
    transition: all 0.3s ease;
    text-decoration: none;
}

.td-footer-wrapper a:hover {
    color: var(--bawana-button-hover) !important;
    text-decoration: underline;
}

.td-footer-wrapper .widget-title {
    color: var(--bawana-text-primary) !important;
    border-bottom: 2px solid var(--bawana-primary-blue);
    padding-bottom: 8px;
    margin-bottom: 15px;
    font-weight: 600;
}

.td-footer-wrapper .widget {
    margin-bottom: 25px;
}

.td-footer-wrapper .widget ul li {
    border-bottom: 1px solid var(--bawana-border-light);
    padding: 8px 0;
}

.td-footer-wrapper .widget ul li:last-child {
    border-bottom: none;
}

/* Footer Text */
.td-sub-footer-copy {
    text-align: center;
    color: var(--bawana-text-muted) !important;
    background: var(--bawana-bg-light) !important;
}

/* Styling untuk Video Container di Sidebar */
.bawana-video-container {
    position: relative;
    width: 100%;
    margin-bottom: 15px;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.bawana-video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

/* ============================================================================
   WIDGET STYLING - LIGHT PROFESSIONAL THEME
   ============================================================================ */

/* Styling untuk widget sidebar - Light Professional */
.widget.bawana_live_streaming,
.widget.bawana_talkshow {
    margin-bottom: 30px;
    background: var(--bawana-bg-primary);
    border: 1px solid var(--bawana-border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--bawana-shadow-md);
}

.widget.bawana_live_streaming .block-title span {
    background: var(--bawana-primary-blue);
    color: var(--bawana-bg-primary);
    padding: 12px 20px;
    display: block;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
    font-size: 14px;
}

.widget.bawana_talkshow .block-title span {
    background: var(--bawana-primary-gold);
    color: var(--bawana-bg-primary);
    padding: 12px 20px;
    display: block;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
    font-size: 14px;
}

.widget.bawana_live_streaming .bawana-video-container,
.widget.bawana_talkshow .bawana-video-container {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
}

/* Styling untuk Popular Posts Widget - Light Professional */
.widget.bawana_popular_posts {
    margin-bottom: 30px;
    background: var(--bawana-bg-primary);
    border: 1px solid var(--bawana-border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--bawana-shadow-md);
}

.widget.bawana_popular_posts .block-title span {
    background: var(--bawana-primary-blue);
    color: var(--bawana-bg-primary);
    padding: 12px 20px;
    display: block;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
    font-size: 14px;
}

.bawana-popular-posts {
    padding: 0;
}

.popular-post-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 20px;
    border-bottom: 1px solid var(--bawana-border-light);
    transition: background-color 0.3s ease;
}

.popular-post-item:hover {
    background-color: var(--bawana-bg-light);
}

.popular-post-item:last-child {
    border-bottom: none;
}

.popular-number {
    background: var(--bawana-primary-blue);
    color: var(--bawana-bg-primary);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-right: 12px;
    flex-shrink: 0;
}

.popular-thumb {
    width: 60px;
    height: 45px;
    margin-right: 12px;
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 4px;
}

.popular-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.popular-post-item:hover .popular-thumb img {
    transform: scale(1.05);
}

.popular-content {
    flex: 1;
    min-width: 0;
}

.popular-content h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    line-height: 1.4;
    font-weight: 600;
}

.popular-content h4 a {
    color: var(--bawana-text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.popular-content h4 a:hover {
    color: var(--bawana-primary-blue);
}

.popular-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 11px;
    color: var(--bawana-text-muted);
}

.popular-date,
.popular-comments {
    font-size: 11px;
    color: var(--bawana-text-muted);
}

/* Widget placeholder styling - Light Professional */
.bawana-placeholder {
    background: var(--bawana-bg-light);
    color: var(--bawana-text-secondary);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 2px dashed var(--bawana-border-color);
    margin: 15px;
}

.bawana-placeholder h4 {
    color: var(--bawana-text-primary) !important;
    margin-bottom: 10px;
    font-size: 14px;
}

.bawana-placeholder p {
    color: var(--bawana-text-muted) !important;
    font-size: 12px;
    margin-bottom: 10px;
}

.bawana-placeholder a {
    color: var(--bawana-primary-blue) !important;
    text-decoration: underline;
    font-weight: 600;
}

.bawana-placeholder a:hover {
    color: var(--bawana-button-hover) !important;
}

/* ============================================================================
   ADDITIONAL LIGHT THEME OVERRIDES
   ============================================================================ */

/* Ensure all text elements use proper light theme colors */
.td-post-content p,
.td-post-content div,
.td-post-content span,
.td-excerpt {
    color: var(--bawana-text-secondary) !important;
}

/* Article titles and headings */
h1, h2, h3, h4, h5, h6,
.entry-title,
.td-post-title {
    color: var(--bawana-text-primary) !important;
}

/* Meta information */
.td-post-author-name,
.td-post-date,
.td-post-views,
.td-post-comments,
.td-module-meta-info {
    color: var(--bawana-text-muted) !important;
}

/* Sidebar widget titles */
.widget-title,
.block-title span {
    color: var(--bawana-text-primary) !important;
}

/* General widget content */
.widget {
    background: var(--bawana-bg-primary) !important;
    color: var(--bawana-text-secondary) !important;
}

/* ============================================================================
   ENHANCED VISUAL EFFECTS & ANIMATIONS
   ============================================================================ */

/* Smooth Page Transitions */
.td-main-content-wrap {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Animation for Images */
.td-module-thumb img {
    transition: opacity 0.3s ease;
}

.td-module-thumb img:not(.loaded) {
    opacity: 0.7;
}

/* Parallax Effect for Header */
.td-header-wrap {
    position: relative;
    overflow: hidden;
}

.td-header-wrap::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(30, 58, 138, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

/* Enhanced Card Hover Effects */
.td-module-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
    overflow: hidden;
}

.td-module-container:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.15);
}

/* Glowing Effect for Active Elements */
.td-read-more a,
.td-post-category {
    position: relative;
    overflow: hidden;
}

.td-read-more a::before,
.td-post-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.td-read-more a:hover::before,
.td-post-category:hover::before {
    left: 100%;
}

/* Pulse Animation for Important Elements */
.widget.bawana_live_streaming .block-title span {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(30, 58, 138, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(30, 58, 138, 0); }
    100% { box-shadow: 0 0 0 0 rgba(30, 58, 138, 0); }
}

/* Smooth Scroll Behavior */
html {
    scroll-behavior: smooth;
}

/* Enhanced Focus States for Accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus {
    outline: 3px solid var(--bawana-primary-gold);
    outline-offset: 2px;
}

/* ============================================================================
   FORCE VISIBILITY - KONTRAS MAKSIMAL UNTUK SEMUA TEKS
   ============================================================================ */

/* Header visibility - HANYA untuk bawana header */
.bawana-header-structure,
.bawana-top-menu,
.bawana-logo-section {
    visibility: visible !important;
    opacity: 1 !important;
}

.td-header-wrap .td-logo-text,
.td-header-wrap .td-logo-text * {
    color: var(--bawana-primary-blue) !important;
}

.td-header-wrap .td-tagline-text,
.td-header-wrap .td-tagline-text * {
    color: var(--bawana-gray) !important;
}

/* Menu lama DIHAPUS - Hanya gunakan bawana-main-menu */

/* Pastikan teks konten terlihat */
.td-post-content,
.td-post-content p,
.td-post-content div,
.td-post-content span {
    color: var(--bawana-text-color) !important;
    background: transparent !important;
}

/* Pastikan judul post terlihat */
.entry-title,
.entry-title a,
.td-module-title a,
.td-post-title a {
    color: var(--bawana-dark-blue) !important;
    background: transparent !important;
}

/* Pastikan meta info terlihat */
.td-post-author-name,
.td-post-author-name a,
.td-post-date,
.td-module-meta-info,
.td-module-meta-info a {
    color: var(--bawana-gray) !important;
    background: transparent !important;
}

/* Force visibility untuk semua link */
a {
    color: var(--bawana-primary-blue) !important;
    text-decoration: none !important;
}

a:hover {
    color: var(--bawana-primary-gold) !important;
}

/* Pastikan widget text terlihat */
.widget,
.widget p,
.widget div,
.widget span,
.widget li {
    color: var(--bawana-text-color) !important;
    background: transparent !important;
}

.widget a {
    color: var(--bawana-primary-blue) !important;
}

.widget a:hover {
    color: var(--bawana-primary-gold) !important;
}

/* ============================================================================
   WIDGET BAWANA NEWS - STYLING KHUSUS
   ============================================================================ */

/* Widget Placeholder Styling */
.bawana-video-placeholder,
.bawana-popular-placeholder {
    margin: 15px 0;
}

.bawana-video-placeholder p,
.bawana-popular-placeholder p {
    margin: 0 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Live Streaming Widget */
.widget.bawana_live_streaming {
    background: var(--bawana-gradient-primary);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(30, 58, 138, 0.2);
}

.widget.bawana_live_streaming .block-title {
    background: rgba(255, 255, 255, 0.1);
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.widget.bawana_live_streaming .block-title span {
    color: var(--bawana-white) !important;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.widget.bawana_live_streaming .bawana-video-container {
    padding: 20px;
}

.widget.bawana_live_streaming iframe {
    width: 100% !important;
    height: auto !important;
    min-height: 200px;
    border-radius: 8px;
}

/* Popular Posts Widget */
.widget.bawana_popular_posts {
    background: var(--bawana-white);
    border: 1px solid var(--bawana-border-color);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.widget.bawana_popular_posts .block-title {
    background: var(--bawana-primary-gold);
    margin: 0;
    padding: 15px 20px;
}

.widget.bawana_popular_posts .block-title span {
    color: var(--bawana-white) !important;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.bawana-popular-posts {
    padding: 0;
}

.popular-post-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--bawana-border-color);
    transition: background-color 0.3s ease;
}

.popular-post-item:last-child {
    border-bottom: none;
}

.popular-post-item:hover {
    background-color: var(--bawana-light-gray);
}

.popular-number {
    background: var(--bawana-primary-blue);
    color: var(--bawana-white);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    margin-right: 15px;
    flex-shrink: 0;
}

.popular-thumb {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    flex-shrink: 0;
}

.popular-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.popular-content {
    flex: 1;
}

.popular-content h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.popular-content h4 a {
    color: var(--bawana-dark-blue) !important;
    text-decoration: none;
    font-weight: 600;
}

.popular-content h4 a:hover {
    color: var(--bawana-primary-gold) !important;
}

.popular-meta {
    font-size: 12px;
    color: var(--bawana-gray);
}

.popular-meta span {
    margin-right: 10px;
}

/* Talkshow Widget */
.widget.bawana_talkshow {
    background: linear-gradient(135deg, var(--bawana-primary-gold), var(--bawana-light-gold));
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.widget.bawana_talkshow .block-title {
    background: rgba(0, 0, 0, 0.1);
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.widget.bawana_talkshow .block-title span {
    color: var(--bawana-white) !important;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 1px;
    text-transform: uppercase;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.widget.bawana_talkshow .bawana-video-container {
    padding: 20px;
}

.widget.bawana_talkshow iframe {
    width: 100% !important;
    height: auto !important;
    min-height: 200px;
    border-radius: 8px;
}

/* ============================================================================
   RESPONSIVE DESIGN - MOBILE FIRST APPROACH
   ============================================================================ */

/* Base Mobile Styles (320px+) */
.bawana-video-container iframe {
    max-width: 100%;
    height: auto;
}

/* Ensure all videos are responsive */
.widget iframe,
.td-post-content iframe {
    max-width: 100% !important;
    height: auto !important;
}

/* Mobile Portrait (up to 480px) */
@media (max-width: 480px) {
    .td-logo-text {
        font-size: 16px;
    }

    .td-tagline-text {
        font-size: 10px;
    }

    .bawana-video-container {
        padding-bottom: 65%; /* Taller aspect ratio for mobile */
        margin-bottom: 10px;
    }

    .popular-post-item {
        padding: 10px 12px;
        flex-direction: column;
        align-items: flex-start;
    }

    .popular-number {
        width: 20px;
        height: 20px;
        font-size: 10px;
        margin-right: 8px;
        margin-bottom: 8px;
    }

    .popular-thumb {
        width: 100%;
        height: 120px;
        margin-right: 0;
        margin-bottom: 8px;
    }

    .popular-content h4 {
        font-size: 11px;
        line-height: 1.3;
    }

    .popular-meta {
        font-size: 10px;
    }

    /* Hide less important widgets on very small screens */
    .widget.bawana_talkshow {
        display: none;
    }
}

/* Mobile Landscape & Small Tablets (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .td-logo-text {
        font-size: 18px;
    }

    .bawana-video-container {
        padding-bottom: 60%;
    }

    .popular-post-item {
        padding: 12px 15px;
    }

    .popular-number {
        width: 25px;
        height: 25px;
        font-size: 12px;
        margin-right: 10px;
    }

    .popular-thumb {
        width: 50px;
        height: 38px;
        margin-right: 10px;
    }

    .popular-content h4 {
        font-size: 12px;
    }
}

/* Tablet Portrait (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .td-pb-span4.td-main-sidebar {
        margin-top: 30px;
    }

    .widget {
        margin-bottom: 25px;
    }

    .popular-post-item {
        padding: 14px 18px;
    }

    .bawana-video-container {
        padding-bottom: 56.25%; /* Standard 16:9 */
    }
}

/* Desktop & Large Screens (1025px+) */
@media (min-width: 1025px) {
    .widget {
        margin-bottom: 30px;
    }

    .popular-post-item:hover {
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(30, 58, 138, 0.1);
    }

    .bawana-video-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }
}

/* ============================================================================
   MOBILE MENU & NAVIGATION OPTIMIZATIONS
   ============================================================================ */

@media (max-width: 767px) {
    /* Ensure mobile menu is accessible */
    #td-mobile-nav {
        background: var(--bawana-dark-blue);
    }

    #td-mobile-nav .td-mobile-main-menu li a {
        color: var(--bawana-white);
        border-bottom: 1px solid var(--bawana-primary-blue);
    }

    #td-mobile-nav .td-mobile-main-menu li a:hover {
        background: var(--bawana-primary-blue);
        color: var(--bawana-light-gold);
    }

    /* Mobile header adjustments */
    .td-header-wrap {
        padding: 10px 0;
    }

    .td-main-menu-logo {
        text-align: center;
    }
}

/* ============================================================================
   PRINT STYLES
   ============================================================================ */

@media print {
    .td-main-sidebar,
    .widget.bawana_live_streaming,
    .widget.bawana_talkshow {
        display: none !important;
    }

    .td-pb-span8.td-main-content {
        width: 100% !important;
    }

    .bawana-video-container {
        display: none;
    }
}

/* ============================================================================
   LATEST STORIES SECTION - PERBAIKAN CSS YANG HILANG
   ============================================================================ */

.latest-stories-section {
    background: #ffffff;
    padding: 40px 20px;
    margin: 40px 0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.latest-stories-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 3px solid #1A1A1A;
}

.latest-stories-section .section-title {
    font-size: 28px;
    font-weight: 700;
    color: #1A1A1A;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.latest-stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.latest-story-item {
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    min-height: 140px;
}

.latest-story-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.latest-story-item .story-image {
    flex: 0 0 40%;
    position: relative;
    overflow: hidden;
}

.latest-story-item .story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.latest-story-item:hover .story-image img {
    transform: scale(1.05);
}

.latest-story-item .story-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #ffffff;
}

.latest-story-item .story-category {
    background: #D4AF37;
    color: #ffffff;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-bottom: 10px;
    width: fit-content;
}

.latest-story-item .story-title {
    font-size: 16px;
    font-weight: 700;
    color: #1A1A1A;
    line-height: 1.4;
    margin: 0 0 10px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.latest-story-item .story-title a {
    color: #1A1A1A;
    text-decoration: none;
    transition: color 0.3s ease;
}

.latest-story-item .story-title a:hover {
    color: #D4AF37;
}

.latest-story-item .story-excerpt {
    font-size: 14px;
    color: #666666;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .latest-stories-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .latest-story-item {
        min-height: 120px;
    }

    .latest-story-item .story-content {
        padding: 15px;
    }

    .latest-story-item .story-title {
        font-size: 15px;
    }

    .latest-stories-section .section-title {
        font-size: 24px;
    }
}

/* ============================================================================
   ADDITIONAL HEADER OVERRIDES - TRAVEL BIRDIE STYLE
   ============================================================================ */

/* Ensure new header structure is visible and properly styled */
.bawana-header-structure {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1000 !important;
}

.bawana-top-menu,
.bawana-logo-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* FORCE CLEAN HEADER - HAPUS SEMUA ELEMEN LAMA */
.td-search-wrap,
.td-drop-down-search,
.td-header-search-wrap,
.td-search-form,
.td-search-input,
.td-search-button,
.td-header-menu-wrap-full,
.td-header-menu-wrap,
.td-header-row,
.td-header-main-menu,
.td-main-menu,
.sf-menu,
.td-header-menu-social,
.td-header-sp-top-menu,
.td-header-top-menu,
.td-header-top-menu-full {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
}

/* FORCE BAWANA HEADER ONLY - HAPUS SEMUA YANG LAIN */
#td-top-mobile-toggle,
.td-mobile-nav,
.td-mobile-container,
.td-header-mobile-wrap {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* PASTIKAN HANYA BAWANA HEADER YANG MUNCUL */
.bawana-header-structure {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 9999 !important;
}

.bawana-top-menu {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.bawana-logo-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

@media (max-width: 767px) {
    /* Mobile toggle dihapus, menu tetap horizontal */
    .bawana-top-menu {
        padding: 6px 15px;
    }
}
