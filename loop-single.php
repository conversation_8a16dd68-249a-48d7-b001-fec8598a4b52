<?php
/**
 * The single post loop Default template - BAWANA NEWS CUSTOM
 **/

if (have_posts()) {
    the_post(); ?>
    <article class="<?php echo join(' ', get_post_class());?>">
        <div class="td-post-header">
            <ul class="td-category">
                <?php
                $categories = get_the_category();
                if( !empty( $categories ) ) {
                    foreach($categories as $category) {
                        $cat_link = get_category_link($category->cat_ID);
                        $cat_name = $category->name; ?>
                        <li class="entry-category"><a href="<?php echo esc_url($cat_link) ?>"><?php echo esc_html($cat_name) ?></a></li>
                    <?php }
                } ?>
            </ul>

            <header class="td-post-title">
                <!-- title -->
                <h3 class="entry-title td-module-title">
                    <a href="<?php the_permalink() ?>" rel="bookmark" title="<?php the_title_attribute() ?>">
                        <?php the_title() ?>
                    </a>
                </h3>

                <div class="td-module-meta-info">
                    <!-- author -->
                    <div class="td-post-author-name">
                        <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta( 'ID' ))) ?>"><?php the_author() ?></a>
                        <div class="td-author-line"> - </div>
                    </div>

                    <!-- date -->
                    <span class="td-post-date">
                        <time class="entry-date updated td-module-date" datetime="<?php echo esc_html(date(DATE_W3C, get_the_time('U'))) ?>" ><?php the_time(get_option('date_format')) ?></time>
                    </span>

                    <!-- comments - DINONAKTIFKAN UNTUK BAWANA NEWS -->
                    <!--
                    <div class="td-post-comments">
                        <a href="<?php comments_link() ?>">
                            <i class="td-icon-comments"></i>
                            <?php comments_number('0','1','%') ?>
                        </a>
                    </div>
                    -->
                </div>
            </header>

            <div class="td-post-content tagdiv-type">
                <!-- image -->
                <?php
                    if( get_the_post_thumbnail_url(null, 'full') != false ) { ?>
                        <div class="td-post-featured-image">
                            <?php if(get_the_post_thumbnail_caption() != '' ) { ?>
                                <figure>
                                    <img class="entry-thumb" src="<?php echo esc_url(get_the_post_thumbnail_url(null, 'medium_large')) ?>" alt="<?php the_title() ?>" title="<?php echo esc_attr(strip_tags(the_title())) ?>" />
                                    <figcaption class="wp-caption-text"><?php echo esc_html(get_the_post_thumbnail_caption()) ?></figcaption>
                                </figure>
                            <?php } else { ?>
                                <img class="entry-thumb" src="<?php echo esc_url(get_the_post_thumbnail_url(null, 'medium_large')) ?>" alt="<?php the_title() ?>" title="<?php echo esc_attr(strip_tags(the_title())) ?>" />
                            <?php } ?>
                        </div>
                <?php } ?>

                <?php the_content() ?>
            </div>

            <footer>
                <?php
                    // post pagination
                    wp_link_pages(array(
                        'before' => '<div class="page-nav page-nav-post td-pb-padding-side">',
                        'after' => '</div>',
                        'link_before' => '<div>',
                        'link_after' => '</div>',
                        'nextpagelink'     => '<i class="td-icon-menu-right"></i>',
                        'previouspagelink' => '<i class="td-icon-menu-left"></i>',
                    ));

                    // tags
                    $td_post_tags = get_the_tags();
                    if( !empty($td_post_tags) ) { ?>
                        <div class="td-post-source-tags">
                            <ul class="td-tags td-post-small-box clearfix">
                                <li><span><?php esc_html_e('TAGS', 'newspaper') ?></span></li>
                                <?php
                                    foreach ($td_post_tags as $td_post_tag) { ?>
                                        <li><a href="<?php echo esc_url(get_tag_link($td_post_tag->term_id)) ?>"><?php echo esc_html($td_post_tag->name) ?></a></li>
                                <?php } ?>
                            </ul>
                        </div>
                <?php }
                ?>

                <!-- BAWANA NEWS: TOMBOL SHARE SOSIAL MEDIA -->
                <div class="bawana-social-share">
                    <div class="share-label">Bagikan:</div>
                    <div class="share-buttons">
                        <?php
                        $post_url = urlencode(get_permalink());
                        $post_title = urlencode(get_the_title());
                        $post_excerpt = urlencode(wp_trim_words(get_the_excerpt(), 20));
                        ?>
                        
                        <!-- WhatsApp -->
                        <a href="https://wa.me/?text=<?php echo $post_title; ?>%20<?php echo $post_url; ?>" 
                           target="_blank" 
                           class="share-btn whatsapp" 
                           title="Bagikan ke WhatsApp">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        
                        <!-- Facebook -->
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo $post_url; ?>" 
                           target="_blank" 
                           class="share-btn facebook" 
                           title="Bagikan ke Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        
                        <!-- Twitter/X -->
                        <a href="https://twitter.com/intent/tweet?text=<?php echo $post_title; ?>&url=<?php echo $post_url; ?>" 
                           target="_blank" 
                           class="share-btn twitter" 
                           title="Bagikan ke Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        
                        <!-- Copy Link -->
                        <a href="#" 
                           class="share-btn copy-link" 
                           data-url="<?php echo get_permalink(); ?>" 
                           title="Salin Link">
                            <i class="fas fa-link"></i>
                        </a>
                    </div>
                </div>

                <?php
                    // next/prev posts - TEKS DIPERBAIKI KE BAHASA INDONESIA
                    $next_post = get_next_post();
                    $prev_post = get_previous_post();

                    if (!empty($next_post) or !empty($prev_post)) { ?>
                        <div class="td-block-row td-post-next-prev">
                            <?php if (!empty($prev_post)) { ?>
                                <div class="td-block-span6 td-post-prev-post">
                                    <div class="td-post-next-prev-content">
                                        <span>Artikel Sebelumnya</span>
                                        <a href="<?php echo esc_url(get_permalink($prev_post->ID)) ?>"><?php echo esc_html(get_the_title($prev_post->ID)) ?></a>
                                    </div>
                                </div>
                            <?php } else { ?>
                                <div class="td-block-span6 td-post-prev-post"></div>
                            <?php } ?>

                            <div class="td-next-prev-separator"></div>

                        <?php if (!empty($next_post)) { ?>
                            <div class="td-block-span6 td-post-next-post">
                                <div class="td-post-next-prev-content">
                                    <span>Artikel Berikutnya</span>
                                    <a href="<?php echo esc_url(get_permalink($next_post->ID)) ?>"><?php echo esc_html(get_the_title($next_post->ID)) ?></a>
                                </div>
                            </div>
                        <?php } ?>
                        </div>
                <?php } ?>

                <!-- author box -->
                <?php
                $author_id = get_the_author_meta( 'ID' );
                ?>
                <div class="author-box-wrap">
                    <a href="<?php echo esc_url(get_author_posts_url($author_id)) ?>">
                        <?php echo get_avatar(get_the_author_meta('email', $author_id), '96') ?>
                    </a>
                    <div class="desc">
                        <div class="td-author-name">
                            <a href="<?php echo esc_url(get_author_posts_url($author_id)) ?>"><?php the_author() ?></a>
                        </div>
                        <div class="td-author-description"><?php the_author_meta('description', $author_id) ?></div>
                        <div class="td-author-url">
                            <a href="<?php echo esc_url(get_author_posts_url($author_id)) ?>">Lihat semua artikel</a>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
            </footer>
        </div>
    </article>
<?php } ?>
