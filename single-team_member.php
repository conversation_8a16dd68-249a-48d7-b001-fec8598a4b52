<?php get_header(); ?>

<div class="td-main-content-wrap td-container-wrap">
    <div class="td-container">
        <div class="td-pb-row">
            <div class="td-pb-span8 td-main-content">
                <div class="td-ss-main-content">
                    <div class="td-post-header">
                        <div class="team-member-profile-header">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="team-member-profile-image">
                                    <?php the_post_thumbnail('large'); ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="team-member-profile-info">
                                <h1 class="entry-title"><?php the_title(); ?></h1>
                                <div class="td-module-meta-info">
                                    <span class="team-member-position"><?php the_field('jabatan'); ?></span>
                                </div>
                                
                                <!-- Breadcrumb back to team page -->
                                <div class="team-breadcrumb">
                                    <a href="<?php echo home_url('/dewan-redaksi'); ?>" class="back-to-team">← Kembali ke Tim Redaksi</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="td-post-content">
                        <?php 
                        if (have_posts()) : 
                            while (have_posts()) : the_post();
                                // Tampilkan konten biografi jika ada
                                if (get_the_content()) {
                                    the_content();
                                } else {
                                    echo '<p><em>Profil lengkap akan segera ditambahkan.</em></p>';
                                }
                            endwhile;
                        endif;
                        ?>
                    </div>
                    
                    <!-- Navigation ke anggota tim lainnya -->
                    <div class="team-navigation">
                        <?php
                        $prev_post = get_previous_post(false, '', 'team_member');
                        $next_post = get_next_post(false, '', 'team_member');
                        
                        if ($prev_post || $next_post) :
                        ?>
                            <div class="team-nav-links">
                                <?php if ($prev_post) : ?>
                                    <div class="team-nav-prev">
                                        <a href="<?php echo get_permalink($prev_post->ID); ?>">
                                            <span class="nav-label">← Sebelumnya</span>
                                            <span class="nav-title"><?php echo get_the_title($prev_post->ID); ?></span>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($next_post) : ?>
                                    <div class="team-nav-next">
                                        <a href="<?php echo get_permalink($next_post->ID); ?>">
                                            <span class="nav-label">Selanjutnya →</span>
                                            <span class="nav-title"><?php echo get_the_title($next_post->ID); ?></span>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="td-pb-span4 td-main-sidebar">
                <?php get_sidebar(); ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
