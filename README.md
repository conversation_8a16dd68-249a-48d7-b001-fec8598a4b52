# 🗞️ Bawana News - WordPress Child Theme

## 📖 Deskripsi
Child theme khusus untuk website **Bawana News** yang dibangun di atas tema **Newspaper v12.7.1** oleh tagDiv. Theme ini dirancang khusus untuk kemudahan operator non-teknis dalam mengelola konten berita.

## ✨ Fitur Utama

### 🎨 Branding Global Bawana News
- **CSS Variables**: Sistem warna terpusat dengan custom properties
- **Palet Konsisten**: Bir<PERSON> (#1e3a8a) dan <PERSON> (#d4af37) di seluruh situs
- **Gradient Effects**: Gradien profesional untuk header, widget, dan tombol
- **Theme Integration**: Override warna tema default dengan branding Bawana News
- **Typography**: Font profesional untuk readability optimal

### 📱 Kontrol Sidebar Fleksibel
- **Default Sidebar**: Semua halaman menggunakan sidebar secara default
- **Per-Page Control**: Admin dapat mengatur sidebar per halaman/post individual
- **Role-Based Access**: Editor tidak dapat mengubah struktur widget (kese<PERSON><PERSON><PERSON>)
- **Template Override**: Custom single.php dan page.php dengan kontrol dinamis
- **Meta Box Admin**: Interface sederhana untuk mengatur layout sidebar

### 🚀 Optimasi Responsif Mobile-First
- **Mobile-First Design**: Pendekatan desain dimulai dari mobile ke desktop
- **Responsive Videos**: Semua iframe video otomatis responsive dengan aspect ratio
- **Touch Optimizations**: Target sentuh minimal 44px untuk aksesibilitas
- **Performance**: Lazy loading, preconnect hints, dan critical CSS inline
- **Adaptive Layout**: Widget menyesuaikan tampilan berdasarkan ukuran layar
- **JavaScript Optimizations**: Script khusus untuk pengalaman mobile yang smooth

### 📺 Video Sidebar Management
- **Live Streaming Widget**: Kelola video live streaming dari Theme Panel
- **Talkshow Widget**: Kelola video talkshow dari Theme Panel
- **Centralized Control**: Satu tempat untuk mengatur semua video embed
- **Responsive Design**: Video menyesuaikan ukuran layar otomatis

### 📊 Popular Posts Widget
- **Auto-Generated**: Berita populer otomatis berdasarkan engagement
- **5 Top Posts**: Menampilkan 5 berita paling populer
- **Visual Ranking**: Nomor urutan dengan design menarik
- **Thumbnail Preview**: Gambar mini untuk setiap berita

### 🖥️ Simplified Admin Interface
- **Clean Editor**: Interface editor disederhanakan untuk operator
- **Hidden Complexity**: Fitur teknis disembunyikan dari non-admin
- **Visual Guidance**: Icon dan label yang jelas untuk setiap fungsi
- **Streamlined Menu**: Menu admin yang fokus pada content creation

## 📁 Struktur File

```
newspaper-child/
├── style.css                      # Styling utama dengan CSS Variables dan responsive design
├── functions.php                  # Fungsi PHP, widget custom, dan optimasi
├── single.php                     # Template single post dengan kontrol sidebar dinamis
├── page.php                       # Template page dengan kontrol sidebar dinamis
├── js/
│   └── bawana-mobile-optimizations.js  # JavaScript untuk optimasi mobile
├── PANDUAN-OPERATOR.md            # Panduan lengkap untuk operator
├── TESTING-CHECKLIST.md           # Checklist testing sebelum production
└── README.md                     # Dokumentasi teknis (file ini)
```

## 🚀 Instalasi

### Persyaratan
- WordPress 5.0+
- Tema Newspaper v12.7.1 (parent theme)
- PHP 7.4+
- MySQL 5.6+

### Langkah Instalasi
1. **Download** folder `newspaper-child`
2. **Upload** ke `/wp-content/themes/newspaper-child/`
3. **Aktivasi** theme di Dashboard → Appearance → Themes
4. **Setup otomatis** akan berjalan untuk konfigurasi sidebar

## ⚙️ Konfigurasi

### Video Sidebar Setup
1. Masuk ke **Dashboard → Newspaper → Video Sidebar**
2. Input kode embed YouTube/Facebook di field yang tersedia
3. Klik **Simpan Perubahan**

### Widget Sidebar
Widget akan otomatis tersusun dengan urutan:
1. **Live Streaming** (atas)
2. **Berita Populer** (tengah) 
3. **Talkshow** (bawah)

## 🎯 Penggunaan

### Untuk Operator Harian
- Baca **PANDUAN-OPERATOR.md** untuk instruksi lengkap
- Fokus pada: menulis berita, upload gambar, update video

### Untuk Admin/Developer
- Akses penuh ke Theme Panel dan WordPress settings
- Dapat mengubah konfigurasi advanced melalui functions.php

## 🧪 Testing

Sebelum production, jalankan semua test di **TESTING-CHECKLIST.md**:
- ✅ Instalasi theme
- ✅ Video sidebar functionality  
- ✅ Popular posts widget
- ✅ Responsive design
- ✅ User permissions
- ✅ Performance

## 🔧 Customization

### Mengubah Warna Branding
Edit di `style.css`:
```css
/* Warna utama biru */
background: linear-gradient(135deg, #1e3a8a, #3b82f6);

/* Warna aksen emas */
background: linear-gradient(135deg, #d4af37, #f59e0b);
```

### Menambah Widget Baru
Edit di `functions.php`:
```php
// Daftarkan widget baru
register_widget('Your_Custom_Widget');
```

### Mengubah Layout Sidebar
Edit fungsi `bawana_news_setup_sidebar()` di `functions.php`

## 🛡️ Security Features

- **Nonce Verification**: Semua form menggunakan WordPress nonce
- **Data Sanitization**: Input user di-sanitize sebelum disimpan
- **Role-based Access**: Fitur dibatasi berdasarkan user role
- **XSS Protection**: Output di-escape untuk mencegah XSS

## 📱 Responsive Design

### Breakpoints
- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px  
- **Mobile**: 320px - 767px

### Mobile Optimizations
- Video containers menyesuaikan aspect ratio
- Popular posts widget dengan layout compact
- Touch-friendly button sizes

## 🔧 Fitur Teknis Terbaru

### CSS Variables System
```css
:root {
    --bawana-primary-blue: #1e3a8a;
    --bawana-primary-gold: #d4af37;
    --bawana-gradient-primary: linear-gradient(135deg, var(--bawana-primary-blue) 0%, var(--bawana-secondary-blue) 100%);
}
```

### Sidebar Control Meta Box
- **Meta Key**: `_bawana_sidebar_setting`
- **Values**: `default`, `with_sidebar`, `no_sidebar`
- **Access**: Admin only (Editor tidak dapat mengakses)

### Mobile Optimizations
- **Viewport Meta**: Responsive viewport dengan user-scalable
- **Lazy Loading**: Intersection Observer untuk gambar dan iframe
- **Touch Targets**: Minimal 44px untuk aksesibilitas
- **Performance**: Critical CSS inline, preconnect hints

### JavaScript Features
- **File**: `/js/bawana-mobile-optimizations.js`
- **Dependencies**: jQuery
- **Features**: Responsive videos, mobile navigation, lazy loading
- **Performance**: Debounced resize, throttled scroll events

## 🔄 Update & Maintenance

### Child Theme Benefits
- **Safe Updates**: Parent theme bisa diupdate tanpa kehilangan customization
- **Modular**: Customization terpisah dari core theme
- **Backup Friendly**: Mudah di-backup dan restore

### Maintenance Tasks
- Regular backup file theme
- Monitor performance dengan tools seperti GTmetrix
- Update WordPress dan plugins secara berkala
- Test functionality setelah update major
- Monitor mobile performance dengan PageSpeed Insights

## 🆘 Troubleshooting

### Sidebar Tidak Muncul/Hilang
- Cek setting sidebar di meta box post/page (Admin only)
- Pastikan widget sudah diatur di Dashboard > Appearance > Widgets
- Verify template files (single.php, page.php) tidak corrupt
- Clear cache dan refresh halaman

### Warna Branding Tidak Berubah
- Pastikan CSS Variables didukung browser (IE11+ required)
- Clear cache browser dan server
- Cek apakah ada CSS conflict dengan plugin lain
- Verify file style.css ter-load dengan benar

### Mobile Layout Bermasalah
- Test di berbagai device dan browser
- Cek JavaScript console untuk error
- Pastikan file `/js/bawana-mobile-optimizations.js` ter-load
- Verify viewport meta tag ada di `<head>`

### Video Tidak Muncul
- Cek kode embed sudah benar
- Pastikan URL video bisa diakses public
- Clear cache browser dan server
- Test responsive video container

### Popular Posts Kosong
- Pastikan ada post dengan featured image
- Cek ada komentar di beberapa post
- Widget akan update otomatis setelah ada engagement

### Performance Issues
- Enable caching plugin (WP Rocket, W3 Total Cache)
- Optimize images dengan plugin seperti Smush
- Monitor dengan GTmetrix dan PageSpeed Insights
- Cek apakah lazy loading berfungsi

### Layout Broken
- Deactivate dan reactivate theme
- Clear all cache
- Cek conflict dengan plugin lain
- Verify parent theme Newspaper masih aktif

## 📞 Support

### Developer Contact
- **Email**: [Developer email]
- **WhatsApp**: [Developer phone]

### Documentation
- **User Guide**: PANDUAN-OPERATOR.md
- **Testing**: TESTING-CHECKLIST.md
- **Technical**: README.md (file ini)

## 📄 License

Child theme ini dibuat khusus untuk Bawana News. Menggunakan parent theme Newspaper yang berlisensi dari tagDiv/ThemeForest.

## 🏆 Credits

- **Parent Theme**: Newspaper by tagDiv
- **Custom Development**: [Developer name]
- **Design Concept**: Bawana News Team
- **Testing**: [Tester names]

---

**🎉 Selamat! Website Bawana News siap menjadi platform berita yang profesional dan mudah dikelola!**
